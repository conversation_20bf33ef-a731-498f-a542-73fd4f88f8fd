import type { RouteRecordRaw } from 'vue-router';

import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'lucide:settings',
      order: 9997,
      title: $t('system.title'),
    },
    name: 'System',
    path: '/system',
    children: [
      {
        path: '/system/sessions',
        name: 'SystemSessions',
        meta: {
          icon: 'lucide:activity',
          title: $t('system.sessions.title'),
        },
        component: () => import('#/views/system/sessions/index.vue'),
      },
    ],
  },
];

export default routes;
