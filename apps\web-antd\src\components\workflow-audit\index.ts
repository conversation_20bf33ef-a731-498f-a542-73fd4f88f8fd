import type {
  WorkflowAuditConfig,
  WorkflowAuditOptions,
  WorkflowAuditResult,
  WorkflowStep,
} from './types';

import { createVNode } from 'vue';

import { useUserStore } from '@vben/stores';

import { Modal } from 'ant-design-vue';

import { callServer, getDataSetNoPage } from '#/api';

import WorkflowAudit from './workflow-audit.vue';

let modalInstance: any = null;

export interface ShowWorkflowAuditOptions extends WorkflowAuditOptions {
  title?: string;
  width?: number | string;
}

/**
 * 显示工作流审批对话框
 * @param options 工作流审批选项
 * @returns Promise<WorkflowAuditResult | null>
 */
export const workflowAudit = async (
  options: ShowWorkflowAuditOptions,
): Promise<null | WorkflowAuditResult> => {
  const {
    workflowCode,
    stepCode,
    stepDetCat,
    title = '工作流审批',
    width = '50vw',
  } = options;

  // 如果已经存在实例，先销毁
  if (modalInstance) {
    modalInstance.destroy();
  }

  if (!workflowCode) {
    throw new Error('workflowCode is required');
  }

  if (!stepCode) {
    throw new Error('stepCode is required');
  }

  try {
    // 获取工作流审批配置
    const config = await getWorkflowAuditConfig(
      workflowCode,
      stepCode,
      stepDetCat || '',
    );

    // 如果没有可选的下一步骤，直接返回
    if (!config.nextSteps || config.nextSteps.length === 0) {
      throw new Error('No available next steps found');
    }

    return new Promise((resolve, _reject) => {
      // 创建新的 Modal 实例
      modalInstance = Modal.info({
        title,
        width,
        icon: null,
        content: () => {
          return createVNode(WorkflowAudit, {
            config: {
              ...config,
              ...options,
            },
            onConfirm: (data: WorkflowAuditResult) => {
              modalInstance.destroy();
              modalInstance = null;
              resolve(data);
            },
            onCancel: () => {
              modalInstance.destroy();
              modalInstance = null;
              resolve(null);
            },
          });
        },
        okText: null,
        cancelText: null,
        maskClosable: false,
        keyboard: false,
        footer: null,
      });
    });
  } catch (error) {
    console.error('Failed to initialize workflow audit:', error);
    throw error;
  }
};

/**
 * 关闭工作流审批对话框
 */
export const workflowAuditEnd = () => {
  if (modalInstance) {
    modalInstance.destroy();
    modalInstance = null;
  }
};

/**
 * 获取工作流审批配置
 * @param workflowCode 工作流代码
 * @param stepCode 步骤代码
 * @param stepDetCat 步骤类别
 * @returns 工作流审批配置
 */
async function getWorkflowAuditConfig(
  workflowCode: string,
  stepCode: string,
  stepDetCat: string,
): Promise<WorkflowAuditConfig> {
  const userStore = useUserStore();

  try {
    // 尝试调用真实的后端接口
    const [esignConfig, nextStepsData] = await Promise.all([
      // 获取电子签名配置
      callServer('GeneralWorkFlowActions.GetWFStep', [
        workflowCode,
        stepCode,
      ]).catch(() => false),
      // 获取下一个可选步骤
      getDataSetNoPage('GeneralWorkFlowActions.cbActionsByStep', [
        workflowCode,
        stepCode,
        stepDetCat,
      ]).catch(() => null),
    ]);

    // 解析电子签名配置
    const [esign, commentLabel] = esignConfig;

    // 处理下一步骤数据
    // 处理下一步骤数据
    const nextSteps: WorkflowStep[] = Array.isArray(nextStepsData)
      ? nextStepsData.map(
          (step: { DISPOSITIONCODE: string; DISPOSITIONNAME: string }) => ({
            stepCode: step.DISPOSITIONCODE,
            stepName: step.DISPOSITIONNAME,
          }),
        )
      : [];

    return {
      esign,
      commentLabel,
      nextSteps,
      username: userStore.userInfo?.userName || '',
    };
  } catch (error) {
    console.error('Failed to get workflow audit config:', error);
    throw error;
  }
}

// 导出类型
export type {
  WorkflowAuditConfig,
  WorkflowAuditOptions,
  WorkflowAuditResult,
  WorkflowStep,
} from './types';
