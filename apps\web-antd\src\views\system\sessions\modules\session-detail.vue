<script lang="ts" setup>
import type { UserSessionDTO } from '#/types/platform/sessions';

import { computed } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Card, Descriptions, DescriptionsItem, Tag } from 'ant-design-vue';
import dayjs from 'dayjs';

import { $t } from '#/locales';

interface Props {
  session?: UserSessionDTO;
}

const props = defineProps<Props>();

const [Modal, modalApi] = useVbenModal({
  onOpenChange: (isOpen: boolean) => {
    if (isOpen) {
      const sessionData = modalApi.getData<UserSessionDTO>();
      if (sessionData) {
        Object.assign(props, { session: sessionData });
      }
    }
  },
});

// 格式化时间
const formatTime = (time: string) => {
  return time ? dayjs(time).format('YYYY-MM-DD HH:mm:ss') : '-';
};

// 设备类型标签颜色
const deviceTypeColor = computed(() => {
  const colorMap = {
    Web: 'blue',
    Mobile: 'green',
    Desktop: 'orange',
    API: 'purple',
  };
  return colorMap[props.session?.deviceType as keyof typeof colorMap] || 'default';
});

// 状态标签颜色
const statusColor = computed(() => {
  const colorMap = {
    Active: 'success',
    Inactive: 'warning',
    Expired: 'error',
  };
  return colorMap[props.session?.status as keyof typeof colorMap] || 'default';
});
</script>

<template>
  <Modal :title="$t('system.sessions.detail.title')" width="800px">
    <div v-if="session" class="space-y-4">
      <!-- 基本信息 -->
      <Card :title="$t('system.sessions.detail.basicInfo')" size="small">
        <Descriptions :column="2" bordered size="small">
          <DescriptionsItem :label="$t('system.sessions.sessionId')">
            <span class="font-mono text-sm">{{ session.id }}</span>
          </DescriptionsItem>
          <DescriptionsItem :label="$t('system.sessions.userId')">
            <span class="font-mono text-sm">{{ session.userId }}</span>
          </DescriptionsItem>
          <DescriptionsItem :label="$t('system.sessions.userName')">
            <span class="font-medium">{{ session.userName }}</span>
          </DescriptionsItem>
          <DescriptionsItem :label="$t('system.sessions.status')">
            <Tag :color="statusColor">{{ session.status }}</Tag>
          </DescriptionsItem>
          <DescriptionsItem :label="$t('system.sessions.loginSource')">
            {{ session.loginSource }}
          </DescriptionsItem>
          <DescriptionsItem :label="$t('system.sessions.location')">
            {{ session.location || '-' }}
          </DescriptionsItem>
        </Descriptions>
      </Card>

      <!-- 设备信息 -->
      <Card :title="$t('system.sessions.detail.deviceInfo')" size="small">
        <Descriptions :column="2" bordered size="small">
          <DescriptionsItem :label="$t('system.sessions.deviceType')">
            <Tag :color="deviceTypeColor">{{ session.deviceType }}</Tag>
          </DescriptionsItem>
          <DescriptionsItem :label="$t('system.sessions.ipAddress')">
            <span class="font-mono">{{ session.ipAddress }}</span>
          </DescriptionsItem>
          <DescriptionsItem :label="$t('system.sessions.operatingSystem')">
            {{ session.operatingSystem }}
          </DescriptionsItem>
          <DescriptionsItem :label="$t('system.sessions.browser')">
            {{ session.browser }}
          </DescriptionsItem>
          <DescriptionsItem :label="$t('system.sessions.userAgent')" :span="2">
            <span class="text-xs text-gray-600 break-all">{{ session.userAgent }}</span>
          </DescriptionsItem>
        </Descriptions>
      </Card>

      <!-- 登录信息 -->
      <Card :title="$t('system.sessions.detail.loginInfo')" size="small">
        <Descriptions :column="2" bordered size="small">
          <DescriptionsItem :label="$t('system.sessions.loginTime')">
            {{ formatTime(session.loginTime) }}
          </DescriptionsItem>
          <DescriptionsItem :label="$t('system.sessions.lastActivityTime')">
            {{ formatTime(session.lastActivityTime) }}
          </DescriptionsItem>
          <DescriptionsItem :label="$t('system.sessions.tokenExpiresAt')">
            {{ formatTime(session.tokenExpiresAt) }}
          </DescriptionsItem>
          <DescriptionsItem :label="$t('system.sessions.isActive')">
            <Tag :color="session.isActive ? 'success' : 'default'">
              {{ session.isActive ? '是' : '否' }}
            </Tag>
          </DescriptionsItem>
        </Descriptions>
      </Card>
    </div>
  </Modal>
</template>

<style scoped>
:deep(.ant-descriptions-item-label) {
  font-weight: 500;
  background-color: #fafafa;
}

:deep(.ant-card-head-title) {
  font-size: 14px;
  font-weight: 600;
}
</style>
