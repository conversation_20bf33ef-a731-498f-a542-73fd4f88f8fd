<script lang="ts" setup>
import type { SessionStatisticsDTO } from '#/types/platform/sessions';

import { computed, onMounted, ref } from 'vue';

import { Page } from '@vben/common-ui';

import { Card, Col, message, Row, Spin, Statistic } from 'ant-design-vue';
import {
  Activity,
  Laptop,
  Monitor,
  Server,
  Smartphone,
  Timer,
  TrendingUp,
  Users,
} from 'lucide-vue-next';

import { getSessionStatistics } from '#/api/platform/sessions';
import { $t } from '#/locales';

defineOptions({
  name: 'SessionDashboard',
});

// 统计数据
const statistics = ref<null | SessionStatisticsDTO>(null);
const loading = ref(false);

/**
 * 加载统计数据
 */
async function loadStatistics() {
  loading.value = true;
  try {
    statistics.value = await getSessionStatistics();
  } catch (error) {
    console.error('Failed to load session statistics:', error);
    message.error('加载统计数据失败');
  } finally {
    loading.value = false;
  }
}

/**
 * 刷新数据
 */
function refreshData() {
  loadStatistics();
}

// 组件挂载时加载数据
onMounted(() => {
  loadStatistics();
});

// 定时刷新数据（每30秒）
setInterval(() => {
  if (!loading.value) {
    loadStatistics();
  }
}, 60_000);

// 计算百分比的辅助函数
const getPercentage = (value: number, total: number) => {
  return total > 0 ? Math.round((value / total) * 100) : 0;
};

// 计算属性
const webPercentage = computed(() =>
  getPercentage(
    statistics.value?.webSessions || 0,
    statistics.value?.totalActiveSessions || 1,
  ),
);

const mobilePercentage = computed(() =>
  getPercentage(
    statistics.value?.mobileSessions || 0,
    statistics.value?.totalActiveSessions || 1,
  ),
);

const desktopPercentage = computed(() =>
  getPercentage(
    statistics.value?.desktopSessions || 0,
    statistics.value?.totalActiveSessions || 1,
  ),
);

const apiPercentage = computed(() =>
  getPercentage(
    statistics.value?.apiSessions || 0,
    statistics.value?.totalActiveSessions || 1,
  ),
);
</script>

<template>
  <Page auto-content-height>
    <div class="p-4">
      <Spin :spinning="loading">
        <!-- 统计卡片 -->
        <Row :gutter="[16, 16]" class="mb-6">
          <Col :xs="24" :sm="12" :md="6">
            <Card>
              <Statistic
                :title="$t('system.sessions.statistics.totalOnlineUsers')"
                :value="statistics?.totalOnlineUsers || 0"
                :value-style="{ color: '#3f8600' }"
              >
                <template #prefix>
                  <Users class="size-4" />
                </template>
              </Statistic>
            </Card>
          </Col>

          <Col :xs="24" :sm="12" :md="6">
            <Card>
              <Statistic
                :title="$t('system.sessions.statistics.totalActiveSessions')"
                :value="statistics?.totalActiveSessions || 0"
                :value-style="{ color: '#1890ff' }"
              >
                <template #prefix>
                  <Activity class="size-4" />
                </template>
              </Statistic>
            </Card>
          </Col>

          <Col :xs="24" :sm="12" :md="6">
            <Card>
              <Statistic
                :title="$t('system.sessions.statistics.todayNewSessions')"
                :value="statistics?.todayNewSessions || 0"
                :value-style="{ color: '#722ed1' }"
              >
                <template #prefix>
                  <TrendingUp class="size-4" />
                </template>
              </Statistic>
            </Card>
          </Col>

          <Col :xs="24" :sm="12" :md="6">
            <Card>
              <Statistic
                :title="$t('system.sessions.statistics.statisticsTime')"
                :value="
                  statistics?.statisticsTime
                    ? new Date(statistics.statisticsTime).toLocaleString()
                    : '-'
                "
              >
                <template #prefix>
                  <Timer class="size-4" />
                </template>
              </Statistic>
              <!-- <div class="text-center">
                <div class="mb-2 text-sm text-gray-500">统计时间</div>
                <div>
                  {{
                    statistics?.statisticsTime
                      ? new Date(statistics.statisticsTime).toLocaleString()
                      : '-'
                  }}
                </div>
              </div> -->
            </Card>
          </Col>
        </Row>

        <!-- 设备类型分布 -->
        <Row :gutter="[16, 16]">
          <Col :xs="24" :lg="12">
            <Card :title="$t('system.sessions.statistics.deviceDistribution')">
              <div class="space-y-4">
                <div
                  class="flex items-center justify-between rounded-lg bg-blue-50 p-3"
                >
                  <div class="flex items-center space-x-3">
                    <Monitor class="size-5 text-blue-600" />
                    <span class="font-medium">{{
                      $t('system.sessions.statistics.webSessions')
                    }}</span>
                  </div>
                  <div class="text-lg font-bold text-blue-600">
                    {{ statistics?.webSessions || 0 }}
                  </div>
                </div>

                <div
                  class="flex items-center justify-between rounded-lg bg-green-50 p-3"
                >
                  <div class="flex items-center space-x-3">
                    <Smartphone class="size-5 text-green-600" />
                    <span class="font-medium">{{
                      $t('system.sessions.statistics.mobileSessions')
                    }}</span>
                  </div>
                  <div class="text-lg font-bold text-green-600">
                    {{ statistics?.mobileSessions || 0 }}
                  </div>
                </div>

                <div
                  class="flex items-center justify-between rounded-lg bg-orange-50 p-3"
                >
                  <div class="flex items-center space-x-3">
                    <Laptop class="size-5 text-orange-600" />
                    <span class="font-medium">{{
                      $t('system.sessions.statistics.desktopSessions')
                    }}</span>
                  </div>
                  <div class="text-lg font-bold text-orange-600">
                    {{ statistics?.desktopSessions || 0 }}
                  </div>
                </div>

                <div
                  class="flex items-center justify-between rounded-lg bg-purple-50 p-3"
                >
                  <div class="flex items-center space-x-3">
                    <Server class="size-5 text-purple-600" />
                    <span class="font-medium">{{
                      $t('system.sessions.statistics.apiSessions')
                    }}</span>
                  </div>
                  <div class="text-lg font-bold text-purple-600">
                    {{ statistics?.apiSessions || 0 }}
                  </div>
                </div>
              </div>
            </Card>
          </Col>

          <Col :xs="24" :lg="12">
            <Card title="会话分布概览">
              <div class="space-y-6">
                <!-- 总计信息 -->
                <div class="rounded-lg bg-gray-50 p-4 text-center">
                  <div class="mb-1 text-2xl font-bold text-gray-800">
                    {{ statistics?.totalActiveSessions || 0 }}
                  </div>
                  <div class="text-sm text-gray-600">总活跃会话数</div>
                </div>

                <!-- 设备类型占比 -->
                <div class="space-y-3">
                  <div class="text-sm font-medium text-gray-700">
                    设备类型占比
                  </div>

                  <div class="space-y-2">
                    <div class="flex items-center justify-between text-sm">
                      <span>Web</span>
                      <span>{{ webPercentage }}%</span>
                    </div>
                    <div class="h-2 w-full rounded-full bg-gray-200">
                      <div
                        class="h-2 rounded-full bg-blue-600"
                        :style="{ width: `${webPercentage}%` }"
                      ></div>
                    </div>
                  </div>

                  <div class="space-y-2">
                    <div class="flex items-center justify-between text-sm">
                      <span>Mobile</span>
                      <span>{{ mobilePercentage }}%</span>
                    </div>
                    <div class="h-2 w-full rounded-full bg-gray-200">
                      <div
                        class="h-2 rounded-full bg-green-600"
                        :style="{ width: `${mobilePercentage}%` }"
                      ></div>
                    </div>
                  </div>

                  <div class="space-y-2">
                    <div class="flex items-center justify-between text-sm">
                      <span>Desktop</span>
                      <span>{{ desktopPercentage }}%</span>
                    </div>
                    <div class="h-2 w-full rounded-full bg-gray-200">
                      <div
                        class="h-2 rounded-full bg-orange-600"
                        :style="{ width: `${desktopPercentage}%` }"
                      ></div>
                    </div>
                  </div>

                  <div class="space-y-2">
                    <div class="flex items-center justify-between text-sm">
                      <span>API</span>
                      <span>{{ apiPercentage }}%</span>
                    </div>
                    <div class="h-2 w-full rounded-full bg-gray-200">
                      <div
                        class="h-2 rounded-full bg-purple-600"
                        :style="{ width: `${apiPercentage}%` }"
                      ></div>
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          </Col>
        </Row>
      </Spin>
    </div>
  </Page>
</template>

<style scoped>
:deep(.ant-statistic-content-prefix) {
  margin-right: 8px;
}

:deep(.ant-card-head-title) {
  font-weight: 600;
}
</style>
