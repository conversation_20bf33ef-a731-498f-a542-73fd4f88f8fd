<script lang="ts" setup>
import type {
  WitLabStep,
  WitLabStepAction,
  WorkflowData,
  WorkflowEdge,
  WorkflowNode,
} from '#/components/logic-flow';

import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { message, Spin } from 'ant-design-vue';

import { callServer, getDataSetNoPage } from '#/api/core/witlab';
import { updateWorkflowStep } from '#/api/settings/workflow';
import WorkflowDesigner from '#/components/logic-flow/WorkflowDesigner.vue';

defineOptions({
  name: 'FlowContainer',
});

const loading = ref(false);
const flowData = ref<WorkflowData>({ nodes: [], edges: [] });
const workflowDesignerRef = ref<InstanceType<typeof WorkflowDesigner>>();

const [FlowModal, flowModalApi] = useVbenModal({
  async onConfirm() {
    flowModalApi.close();
  },
  async onOpenChange(isOpen) {
    if (isOpen) {
      const data = flowModalApi.getData<{ WORKFLOWCODE: string }>();
      if (data) {
        loading.value = true;
        flowData.value = await getWorkflowData(data.WORKFLOWCODE);
        loading.value = false;
      }
    }
  },
  footer: false,
  fullscreen: true,
});

const handleNodeAdd = (node: WorkflowNode) => {
  console.warn('新增节点:', node);
};

const handleNodeDelete = (nodeId: string) => {
  console.warn('删除节点:', nodeId);
};

const handleEdgeAdd = (edge: WorkflowEdge) => {
  console.warn('新增连线:', edge);
};

const handleEdgeDelete = (edgeId: string) => {
  console.warn('删除连线:', edgeId);
};

// 模拟后端API调用
const mockCreateNodes = async (nodes: WorkflowNode[]) => {
  // 模拟网络延迟
  await new Promise((resolve) => setTimeout(resolve, 500));

  // 模拟后端返回的数据，包含数据库ID等
  return nodes.map((node) => ({
    id: node.id,
    dbId: Math.floor(Math.random() * 10_000), // 模拟数据库ID
    createdAt: new Date().toISOString(),
    status: 'active',
    version: 1,
    // 可以添加更多后端返回的字段
  }));
};

// 更新节点的 _data 字段
const updateNodeData = (nodeId: string, newData: any) => {
  const nodeIndex = flowData.value.nodes.findIndex((n) => n.id === nodeId);
  if (nodeIndex !== -1) {
    const node = flowData.value.nodes[nodeIndex];
    // 合并新数据到 _data 字段
    node._data = {
      ...node._data,
      ...newData,
    };
  }
};

// 处理保存事件 - 接收分类的变更数据
const handleSave = async (data: {
  deletedEdges: WorkflowEdge[];
  deletedNodes: WorkflowNode[];
  newEdges: WorkflowEdge[];
  newNodes: WorkflowNode[];
  updatedEdges: WorkflowEdge[];
  updatedNodes: WorkflowNode[];
}) => {
  try {
    // 1. 保存新增的节点
    if (data.newNodes.length > 0) {
      console.warn('开始保存新增节点...');
      const createdNodeData = await mockCreateNodes(data.newNodes);

      // 更新每个新增节点的 _data
      createdNodeData.forEach((nodeData) => {
        updateNodeData(nodeData.id, nodeData);
        console.warn(`节点 ${nodeData.id} 的 _data 已更新:`, nodeData);
      });
    }

    // 2. 保存修改的节点
    if (data.updatedNodes.length > 0) {
      console.warn('开始保存修改节点...');
      data.updatedNodes.forEach(async (node) => {
        const data = node._data;
        await updateWorkflowStep(data);
      });

      // 更新每个修改节点的 _data
      // updatedNodeData.forEach((nodeData) => {
      //   updateNodeData(nodeData.id, nodeData);
      //   console.warn(`节点 ${nodeData.id} 的 _data 已更新:`, nodeData);
      // });
    }

    // 3. 保存新增的连线
    if (data.newEdges.length > 0) {
      // await createEdges(data.newEdges);
      console.warn('保存新增连线（模拟）');
    }

    // 4. 保存修改的连线
    if (data.updatedEdges.length > 0) {
      // await updateEdges(data.updatedEdges);
      console.warn('保存修改连线（模拟）');
    }

    // 5. 删除节点
    if (data.deletedNodes.length > 0) {
      // await deleteNodes(data.deletedNodes.map(n => n.id));
      console.warn('删除节点（模拟）');
    }

    // 6. 删除连线
    if (data.deletedEdges.length > 0) {
      // await deleteEdges(data.deletedEdges.map(e => e.id));
      console.warn('删除连线（模拟）');
    }

    // 7. 同步更新的数据到子组件
    if (workflowDesignerRef.value) {
      // workflowDesignerRef.value.setFlowData(flowData.value);
      console.warn('已同步数据到 WorkflowDesigner 组件');
    }

    // 验证边界情况：新增后删除的元素应该不存在于任何列表中
    const totalChanges =
      data.newNodes.length +
      data.updatedNodes.length +
      data.deletedNodes.length +
      data.newEdges.length +
      data.updatedEdges.length +
      data.deletedEdges.length;

    const summary = [
      `新增节点: ${data.newNodes.length}`,
      `修改节点: ${data.updatedNodes.length}`,
      `删除节点: ${data.deletedNodes.length}`,
      `新增连线: ${data.newEdges.length}`,
      `修改连线: ${data.updatedEdges.length}`,
      `删除连线: ${data.deletedEdges.length}`,
    ].join(', ');

    message.success(`保存成功！${summary}（共 ${totalChanges} 个变更）`);
  } catch (error) {
    message.error('保存失败');
    console.error('保存失败:', error);
  }
};

/**
 * 处理边重叠问题，为双向连接分配不同的锚点
 * 锚点规则：1右、2下、3左、4上
 */
function processEdgesWithAnchors(rawEdges: any[], nodes: any[]) {
  // 创建节点位置映射
  const nodePositions = new Map();
  nodes.forEach((node) => {
    nodePositions.set(node.id, { x: node.x, y: node.y });
  });

  // 追踪已使用的锚点
  const usedAnchors = new Set<string>();

  // 获取锚点位置偏移量
  const getAnchorOffset = (
    anchorType: number,
    nodePos: { x: number; y: number },
  ) => {
    const nodeWidth = 100;
    const nodeHeight = 50;

    switch (anchorType) {
      case 1: {
        // 右锚点
        return { x: nodePos.x + nodeWidth / 2, y: nodePos.y };
      }
      case 2: {
        // 下锚点
        return { x: nodePos.x, y: nodePos.y + nodeHeight / 2 };
      }
      case 3: {
        // 左锚点
        return { x: nodePos.x - nodeWidth / 2, y: nodePos.y };
      }
      case 4: {
        // 上锚点
        return { x: nodePos.x, y: nodePos.y - nodeHeight / 2 };
      }
      default: {
        return { x: nodePos.x, y: nodePos.y };
      }
    }
  };

  // 获取可用的锚点
  const getAvailableAnchor = (
    nodeId: string,
    preferredAnchors: number[],
  ): { anchorId: string; anchorType: number } => {
    for (const anchorType of preferredAnchors) {
      const anchorId = `${nodeId}_${anchorType}`;
      if (!usedAnchors.has(anchorId)) {
        usedAnchors.add(anchorId);
        return { anchorId, anchorType };
      }
    }
    // 如果所有优先锚点都被占用，使用第一个作为备选
    const fallbackType = preferredAnchors[0] || 1; // 提供默认值
    const fallbackId = `${nodeId}_${fallbackType}`;
    return { anchorId: fallbackId, anchorType: fallbackType };
  };

  // 检测双向连接
  const bidirectionalPairs = new Set<string>();
  const edgeMap = new Map<string, any[]>();

  rawEdges.forEach((edge) => {
    const key = `${edge.sourceNodeId}-${edge.targetNodeId}`;
    const reverseKey = `${edge.targetNodeId}-${edge.sourceNodeId}`;

    if (!edgeMap.has(key)) {
      edgeMap.set(key, []);
    }
    const existingEdges = edgeMap.get(key) || [];
    edgeMap.set(key, [...existingEdges, edge]);

    // 检查是否有反向连接
    if (edgeMap.has(reverseKey)) {
      bidirectionalPairs.add([key, reverseKey].sort().join('|'));
    }
  });

  // 为边分配锚点和坐标
  return rawEdges.map((edge) => {
    const { sourceNodeId, targetNodeId } = edge;
    const sourcePos = nodePositions.get(sourceNodeId);
    const targetPos = nodePositions.get(targetNodeId);

    if (!sourcePos || !targetPos) {
      return edge;
    }

    // 检查是否为双向连接
    const pairKey = [
      `${sourceNodeId}-${targetNodeId}`,
      `${targetNodeId}-${sourceNodeId}`,
    ]
      .sort()
      .join('|');
    const isBidirectional = bidirectionalPairs.has(pairKey);

    let sourceAnchor, targetAnchor;

    if (isBidirectional) {
      // 双向连接：根据节点ID大小决定使用不同的锚点避免重叠
      if (sourcePos.x <= targetPos.x) {
        // 使用水平方向连接（右->左）
        sourceAnchor = getAvailableAnchor(sourceNodeId, [1]); // 优先右锚点
        targetAnchor = getAvailableAnchor(targetNodeId, [3]); // 优先左锚点
      } else {
        // 使用垂直方向连接（上->下）避免重叠
        sourceAnchor = getAvailableAnchor(sourceNodeId, [2]); // 优先上锚点
        targetAnchor = getAvailableAnchor(targetNodeId, [2]); // 优先下锚点
      }
    } else {
      // 单向连接：根据节点位置关系智能选择锚点
      if (sourcePos.x <= targetPos.x) {
        // 从左到右：优先使用右->左连接
        sourceAnchor = getAvailableAnchor(sourceNodeId, [1, 2, 4]); // 右、下、上
        targetAnchor = getAvailableAnchor(targetNodeId, [3, 4, 2]); // 左、上、下
      } else {
        // 从右到左：优先使用左->右连接
        sourceAnchor = getAvailableAnchor(sourceNodeId, [2, 4, 3]); // 左、下、上
        targetAnchor = getAvailableAnchor(targetNodeId, [2, 3, 4]); // 右、上、下
      }
    }

    // 计算锚点位置
    const _startPoint = getAnchorOffset(sourceAnchor.anchorType, sourcePos);
    const _endPoint = getAnchorOffset(targetAnchor.anchorType, targetPos);

    // 计算文本位置（连线中点）
    // eslint-disable-next-line no-unused-vars
    const _textX = (_startPoint.x + _endPoint.x) / 2;
    // eslint-disable-next-line no-unused-vars
    const _textY = (_startPoint.y + _endPoint.y) / 2;

    return {
      ...edge,
      sourceAnchorId: sourceAnchor.anchorId,
      targetAnchorId: targetAnchor.anchorId,
      // startPoint,
      // endPoint,
      text: {
        // x: textX,
        // y: textY,
        value: edge.properties?.label || edge.text?.value || '',
      },
      // pointsList: [startPoint, endPoint],
    };
  });
}

const getWorkflowData = async (workflowCode: string): Promise<WorkflowData> => {
  const steps = await getDataSetNoPage(
    'GeneralWorkFlowManager.WORKFLOWSTEPS_GD_NEW',
    [workflowCode],
  );
  const nodes = await Promise.all(
    steps.map(async (item: WitLabStep, index: number) => {
      const menus = await callServer(
        'GeneralWorkFlowManager.GetWorkflowStepMenus.GetWorkflowStepMenusByStepOrigRec',
        [item.ORIGREC],
      );
      const permissions = await callServer(
        'GeneralWorkFlowManager.GetWorkflowStepMenus.GetWorkflowStepPermissionsByStepOrigRec',
        [item.ORIGREC],
      );

      return {
        id: item.STEPCODE,
        type: 'base-rect',
        x: 150 + index * 200,
        y: 200 + index * 50,
        properties: {
          name: item.STEPDISPSTATUS,
        },
        text: {
          x: 150 + index * 200,
          y: 250 + index * 50,
          value: item.STEPNAME,
          editable: false,
        },
        _data: {
          ...item,
          MENUS: menus,
          PERMISSIONS: permissions,
        },
      };
    }),
  );

  const rawEdges = await Promise.all(
    nodes.flatMap(async (item: any) => {
      const stepActions = await getDataSetNoPage(
        'GeneralWorkFlowManager.dgdDetails_NEW',
        [item._data.WORKFLOWCODE, item._data.STEPCODE],
      );

      return stepActions.map((action: WitLabStepAction) =>
        action.TOSTEPCODE
          ? {
              id: action.ORIGREC,
              type: 'polyline',
              sourceNodeId: item._data.STEPCODE,
              targetNodeId: action.TOSTEPCODE,
              properties: {
                // label: action.DISPOSITIONNAME,
                style: {
                  stroke: '#000',
                  strokeWidth: 2,
                },
              },
              text: {
                value: action.DISPOSITIONNAME,
              },
              _data: action,
            }
          : null,
      );
    }),
  );

  // 处理边重叠问题
  const processedEdges = processEdgesWithAnchors(
    rawEdges.flat().filter((edge) => edge !== null),
    nodes,
  );

  return {
    nodes,
    edges: processedEdges,
  };
};
</script>

<template>
  <FlowModal>
    <Spin :spinning="loading" wrapper-class-name="h-full w-full">
      <WorkflowDesigner
        ref="workflowDesignerRef"
        :flow-data="flowData"
        @node-add="handleNodeAdd"
        @node-delete="handleNodeDelete"
        @edge-add="handleEdgeAdd"
        @edge-delete="handleEdgeDelete"
        @save="handleSave"
        class="h-full w-full"
      />
    </Spin>
  </FlowModal>
</template>

<style lang="css" scoped>
:deep(.ant-spin-container) {
  display: flex;
  flex-direction: column;
  width: 100%;
  min-width: 0;
  height: 100%;
  min-height: 0;
}
</style>
