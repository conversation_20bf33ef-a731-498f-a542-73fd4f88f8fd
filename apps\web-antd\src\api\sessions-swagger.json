{"x-generator": "NSwag v14.4.0.0 (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))", "openapi": "3.0.0", "info": {"title": "Witlab.Platform.Web", "version": "1.0.0"}, "servers": [{"url": "http://localhost:5050"}], "paths": {"/api/platform/sessions/{sessionId}/force-logout": {"post": {"tags": ["Sessions", "Platform"], "summary": "强制下线指定会话", "description": "管理员强制下线指定的用户会话", "operationId": "WitlabPlatformWebEndpointsPlatformSessionsForceLogout", "parameters": [{"name": "sessionId", "in": "path", "required": true, "description": "会话ID", "schema": {"type": "string", "format": "guid"}, "example": "7714696f-4261-4ae0-87fa-1d10d8a6427a"}], "requestBody": {"x-name": "ForceLogoutRequest", "description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ForceLogoutRequest"}}}, "required": true, "x-position": 1}, "responses": {"204": {"description": "No Content"}, "400": {"description": "Bad Request", "content": {"application/problem+json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized"}}, "security": [{"JWTBearerAuth": []}]}}, "/api/platform/sessions/user/{userId}/force-logout-all": {"post": {"tags": ["Sessions", "Platform"], "summary": "强制下线用户所有会话", "description": "管理员强制下线指定用户的所有活跃会话", "operationId": "WitlabPlatformWebEndpointsPlatformSessionsForceLogoutAllUserSessions", "parameters": [{"name": "userId", "in": "path", "required": true, "description": "用户ID", "schema": {"type": "string", "format": "guid"}, "example": "3047642f-e963-488e-862b-be95f722594d"}], "requestBody": {"x-name": "ForceLogoutAllUserSessionsRequest", "description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ForceLogoutAllUserSessionsRequest"}}}, "required": true, "x-position": 1}, "responses": {"204": {"description": "No Content"}, "400": {"description": "Bad Request", "content": {"application/problem+json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized"}}, "security": [{"JWTBearerAuth": []}]}}, "/api/platform/sessions/active": {"get": {"tags": ["Sessions", "Platform"], "summary": "获取活跃会话列表", "description": "获取当前所有活跃的用户会话信息，支持分页", "operationId": "WitlabPlatformWebEndpointsPlatformSessionsGetActiveSessions", "parameters": [{"name": "pageNumber", "in": "query", "required": true, "description": "页码", "schema": {"type": "integer", "format": "int32"}, "example": 1}, {"name": "pageSize", "in": "query", "required": true, "description": "页大小", "schema": {"type": "integer", "format": "int32"}, "example": 20}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetActiveSessionsResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/problem+json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/platform/sessions/statistics": {"get": {"tags": ["Sessions", "Platform"], "summary": "获取会话统计信息", "description": "获取当前系统的会话统计信息，包括在线用户数、活跃会话数等", "operationId": "WitlabPlatformWebEndpointsPlatformSessionsGetSessionStats", "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SessionStatisticsDTO"}}}}, "401": {"description": "Unauthorized"}}, "security": [{"JWTBearerAuth": []}]}}, "/api/platform/sessions/user/{userId}": {"get": {"tags": ["Sessions", "Platform"], "summary": "获取指定用户的会话列表", "description": "获取指定用户的所有活跃会话信息", "operationId": "WitlabPlatformWebEndpointsPlatformSessionsGetUserSessions", "parameters": [{"name": "userId", "in": "path", "required": true, "description": "用户ID", "schema": {"type": "string", "format": "guid"}, "example": "c870e859-ac2a-4158-acdb-44787f61d40f"}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetUserSessionsResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/problem+json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized"}}, "security": [{"JWTBearerAuth": []}]}}}, "components": {"schemas": {"EmptyRequest": {"type": "object", "description": "a request dto that doesn't have any properties", "additionalProperties": false}, "ErrorResponse": {"type": "object", "description": "the dto used to send an error response to the client", "additionalProperties": false, "properties": {"statusCode": {"type": "integer", "description": "the http status code sent to the client. default is 400.", "format": "int32", "default": 400}, "message": {"type": "string", "description": "the message for the error response", "default": "One or more errors occurred!"}, "errors": {"type": "object", "description": "the collection of errors for the current context", "additionalProperties": {"type": "array", "items": {"type": "string"}}}}}, "ForceLogoutAllUserSessionsRequest": {"type": "object", "description": "强制下线用户所有会话请求", "example": {"reason": "账户安全检查"}, "additionalProperties": false, "properties": {"reason": {"type": "string", "description": "下线原因", "maxLength": 500, "minLength": 0, "nullable": true, "example": "账户安全检查"}}}, "GetActiveSessionsResponse": {"type": "object", "description": "获取活跃会话响应", "additionalProperties": false, "properties": {"sessions": {"type": "array", "description": "会话列表", "items": {"$ref": "#/components/schemas/UserSessionDTO"}}, "totalCount": {"type": "integer", "description": "总数量", "format": "int32"}, "pageNumber": {"type": "integer", "description": "页码", "format": "int32"}, "pageSize": {"type": "integer", "description": "页大小", "format": "int32"}, "totalPages": {"type": "integer", "description": "总页数", "format": "int32"}}}, "UserSessionDTO": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "string", "format": "guid"}, "userId": {"type": "string", "format": "guid"}, "userName": {"type": "string"}, "tokenId": {"type": "string"}, "loginTime": {"type": "string", "format": "date-time"}, "lastActivityTime": {"type": "string", "format": "date-time"}, "tokenExpiresAt": {"type": "string", "format": "date-time"}, "ipAddress": {"type": "string"}, "userAgent": {"type": "string"}, "deviceType": {"type": "string"}, "operatingSystem": {"type": "string"}, "browser": {"type": "string"}, "location": {"type": "string", "nullable": true}, "status": {"type": "string"}, "loginSource": {"type": "string"}, "isActive": {"type": "boolean"}}}, "GetActiveSessionsRequest": {"type": "object", "description": "获取活跃会话请求", "additionalProperties": false}, "SessionStatisticsDTO": {"type": "object", "additionalProperties": false, "properties": {"totalOnlineUsers": {"type": "integer", "format": "int32"}, "totalActiveSessions": {"type": "integer", "format": "int32"}, "webSessions": {"type": "integer", "format": "int32"}, "mobileSessions": {"type": "integer", "format": "int32"}, "desktopSessions": {"type": "integer", "format": "int32"}, "apiSessions": {"type": "integer", "format": "int32"}, "todayNewSessions": {"type": "integer", "format": "int32"}, "statisticsTime": {"type": "string", "format": "date-time"}}}, "GetUserSessionsResponse": {"type": "object", "description": "获取用户会话响应", "additionalProperties": false, "properties": {"sessions": {"type": "array", "description": "会话列表", "items": {"$ref": "#/components/schemas/UserSessionDTO"}}}}, "GetUserSessionsRequest": {"type": "object", "description": "获取用户会话请求", "additionalProperties": false}, "GetMySessionsResponse": {"type": "object", "description": "获取当前用户会话响应", "additionalProperties": false, "properties": {"sessions": {"type": "array", "description": "会话列表", "items": {"$ref": "#/components/schemas/UserSessionDTO"}}}}, "LogoutOtherSessionsRequest": {"type": "object", "description": "注销其他设备会话请求", "example": {"reason": "安全检查"}, "additionalProperties": false, "properties": {"reason": {"type": "string", "description": "注销原因", "nullable": true, "example": "安全检查"}}}}, "securitySchemes": {"JWTBearerAuth": {"type": "http", "description": "Enter a JWT token to authorize the requests...", "scheme": "Bearer", "bearerFormat": "JWT"}}}}