<script lang="ts" setup>
import type {
  VxeGridListeners,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';
import type { SolutionPreparationApi } from '#/api/dilution-management/solution-preparation';

import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import {
  Button,
  message,
  Select,
  SelectOption,
  Space,
  TabPane,
  Tabs,
} from 'ant-design-vue';
import dayjs from 'dayjs';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  addApprovalRecordsApi,
  addStandMatInventoryApi,
  getFullNameApi,
  getListAttachmentsApi,
  getMatcodeByNameApi,
  getMyUserNameApi,
  getTestsolutionRecordApi,
  getTestSoultionApi,
  updateProviderApi,
} from '#/api/dilution-management/solution-preparation';
import { workflowAudit } from '#/components/workflow-audit';
import { useWorkflowPermission } from '#/hooks/useWorkflowPermission';

import {
  fileColumns,
  historyColumns,
  testColumns,
} from '../solution-preparation-data';
import CommitModal from './commit-modal.vue';
import PreparationModal from './preparation-modal.vue';

const emit = defineEmits(['update:clickRow']);

const [CommitFormModal] = useVbenModal({
  connectedComponent: CommitModal,
  destroyOnClose: true,
});
const [PreparationFormModal] = useVbenModal({
  connectedComponent: PreparationModal,
  destroyOnClose: true,
});
const formargs = [
  'SolutionConfiguration',
  'Approve',
  ' and STATUS = ?',
  "'Approve'",
];
interface RowType {
  [key: string]: any;
}

const gridOptions: VxeTableGridOptions<SolutionPreparationApi.Form> = {
  columns: testColumns(),
  stripe: true,
  border: true,
  editConfig: {
    mode: 'row',
    trigger: 'click',
  },
  height: 'auto',
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async () => {
        const params = [formargs[1]];
        const data = await getTestSoultionApi(params);
        return data;
      },
    },
  },
  exportConfig: {},
  showOverflow: true,
  rowConfig: {
    keyField: 'origrec',
    isCurrent: true,
  },
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: { code: 'query' },
    search: true,
    zoom: true,
  },
};
const historyGridOptions: VxeTableGridOptions<SolutionPreparationApi.Form> = {
  columns: historyColumns(),
  stripe: true,
  border: true,
  editConfig: {
    mode: 'row',
    trigger: 'click',
  },
  height: 'auto',
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async () => {
        if (!clickRow.value.SEQUENCY) {
          return [];
        }

        const data = await getTestsolutionRecordApi([clickRow.value.SEQUENCY]);
        return data;
      },
    },
  },
  exportConfig: {},
  showOverflow: true,
  rowConfig: {
    keyField: 'origrec',
    isCurrent: true,
  },
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: { code: 'query' },
    search: true,
    zoom: true,
  },
};
const fileGridOptions: VxeTableGridOptions<SolutionPreparationApi.Form> = {
  columns: fileColumns(),
  stripe: true,
  border: true,
  editConfig: {
    mode: 'row',
    trigger: 'click',
  },
  height: 'auto',
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async () => {
        if (!clickRow.value.ORIGREC) {
          return [];
        }
        const params = ['DILUTION_STORE', clickRow.value.ORIGREC];
        const data = await getListAttachmentsApi(params);
        return data;
      },
    },
  },
  exportConfig: {},
  showOverflow: true,
  rowConfig: {
    keyField: 'origrec',
    isCurrent: true,
  },
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: { code: 'query' },
    search: true,
    zoom: true,
  },
};
const gridEvents: VxeGridListeners<SolutionPreparationApi.Form> = {
  currentChange: async ({ row }) => {
    if (row) {
      clickRow.value = row;
      emit('update:clickRow', row);
      refreshChildren();
    }
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
  gridEvents,
});
const [HistoryGrid, historyGridApi] = useVbenVxeGrid({
  gridOptions: historyGridOptions,
  gridEvents: {},
});
const [FileGrid, fileGridApi] = useVbenVxeGrid({
  gridOptions: fileGridOptions,
  gridEvents: {},
});
const clickRow = ref<RowType>({});
const activeKey = ref('历史记录');
const tabList = [
  {
    title: '历史记录',
  },
  {
    title: '附件',
  },
];
const hasEditStatus = (row: RowType) => {
  return gridApi.grid?.isEditByRow(row);
};

const editRowEvent = (row: RowType) => {
  gridApi.grid?.setEditRow(row);
};

const saveRowEvent = async () => {
  await gridApi.grid?.clearEdit();
  if (!clickRow.value) {
    message.warning('未选择行，无法保存');
    return;
  }
  const row = clickRow.value;
  const rowList = Object.keys(row).map((key) => {
    const isNum = typeof row[key] === 'number' ? 'N' : 'S';
    return [key, row[key], isNum, ''];
  });
  const params = [
    'dgTestSoution',
    'DILUTION_STORE',
    rowList,
    row.ORIGREC,
    null,
  ];
  await updateProviderApi(params);
};

const cancelRowEvent = (row: RowType) => {
  gridApi.grid?.clearEdit().then(() => {
    // 还原行数据
    gridApi.grid.revertData(row);
  });
};
const tabsChange = () => {
  refreshChildren();
};
const refreshChildren = () => {
  if (activeKey.value === '历史记录') {
    historyGridApi.query();
  }
  if (activeKey.value === '附件') {
    fileGridApi.query();
  }
};
const onRefresh = () => {
  gridApi.query();
  refreshChildren();
};
const viewFiles = () => {
  // TODO：查看文件
};

const batchAddFiles = () => {
  // TODO：批量上传文件
};

const { workflowCode, stepCode } = useWorkflowPermission();

const reviewRecord = async () => {
  if (!clickRow.value.ORIGREC) {
    message.warning('请选择溶液');
    return;
  }

  // TODO:工作流电子签名
 
  // eslint-disable-next-line no-unused-vars, unused-imports/no-unused-vars
  const auditData = await workflowAudit({
    workflowCode: workflowCode.value || '',
    stepCode: stepCode.value || '',
    origRec: clickRow.value.ORIGREC,
    stepDetCat: '',
  });
  // console.log(auditData);

  const eAudit = [false, 'Pass', 'Pass'];
  if (!eAudit[0]) {
    message.success('请进行电子签名');
  }
  const nOrigrec = clickRow.value.ORIGREC;

  const BatchNo = clickRow.value.BATCHNO;
  const ReceiveDate = dayjs().format();
  const Number = clickRow.value.BOTTLENUM;
  const Spec = clickRow.value.SPEC;
  const LocationCode = clickRow.value.LOCATION;
  const dProductDate = clickRow.value.CREATEDATE;
  const dOverDate = clickRow.value.VALIDITYDATE;
  const sPH = clickRow.value.PH;
  const sUsrName = await getMyUserNameApi([]);
  const sPerson = await getFullNameApi([]);
  const sRemark = clickRow.value.REMARKS;
  // const sProductCompany = "";
  // const sMethod = "";
  // const sCerNameandCode = "";
  // const dBeginDate = "";
  // const dEndDate = "";
  // const sInsideCode = "";
  const Purity = clickRow.value.CONSISTENCY;
  const PurityUnit = clickRow.value.UNIT;
  const BatchNum = 1;
  if (eAudit[2] === 'Pass') {
    const matcode = await getMatcodeByNameApi([clickRow.value.DILUTION_NAME]);
    await addStandMatInventoryApi([
      [
        matcode,
        BatchNo,
        '',
        ReceiveDate,
        Number,
        '瓶',
        Spec,
        LocationCode,
        dProductDate,
        dOverDate,
        '',
        sUsrName,
        sPerson,
        sRemark,
        '',
        '',
        '',
        '',
        '',
        '',
        null,
        Purity + PurityUnit,
        BatchNum,
        sPH,
      ],
      clickRow.value.DILUTION_NAME,
    ]);
  }
  const res = await addApprovalRecordsApi([
    [clickRow.value.SEQUENCY],
    [nOrigrec],
  ]);
  if (res) {
    gridApi.query();
  }
};
const selectedReport = ref('试液标签');
const reportOptions = ref([
  {
    value: '试液标签',
    label: '试液标签',
  },
]);
const print = () => {
  // TODO:打印
};
</script>
<template>
  <PreparationFormModal @success="onRefresh" />
  <CommitFormModal @success="onRefresh" />
  <Grid class="h-2/5">
    <template #toolbar-actions>
      <Space :size="[8, 0]" wrap>
        <Button type="primary" @click="reviewRecord">
          {{ $t('dilution-management.solution-preparation-audit.review') }}
        </Button>
      </Space>
    </template>
    <template #action="{ row }">
      <template v-if="hasEditStatus(row)">
        <Button type="link" @click="saveRowEvent()">
          {{ $t('business-static-tables.save') }}
        </Button>
        <Button type="link" @click="cancelRowEvent(row)">
          {{ $t('business-static-tables.cancel') }}
        </Button>
      </template>
      <template v-else>
        <Button type="link" @click="editRowEvent(row)">
          {{ $t('business-static-tables.edit') }}
        </Button>
      </template>
    </template>
    <template #toolbar-tools>
      <span class="px-2">选择报表</span>
      <Select
        placeholder="请选择"
        style="width: 200px"
        v-model:value="selectedReport"
      >
        <SelectOption
          v-for="item in reportOptions"
          :key="item.value"
          :value="item.value"
        >
          {{ item.label }}
        </SelectOption>
      </Select>
      <Button type="link" @click="print()">
        {{ $t('dilution-management.print') }}
      </Button>
    </template>
  </Grid>
  <Tabs v-model:active-key="activeKey" class="w-full" @change="tabsChange">
    <TabPane v-for="item in tabList" :key="item.title" :tab="item.title" />
  </Tabs>
  <HistoryGrid class="h-2/5" v-show="activeKey === '历史记录'" />
  <FileGrid class="h-2/5" v-show="activeKey === '附件'">
    <template #toolbar-actions>
      <Space :size="[8, 0]" wrap>
        <Button type="primary" danger @click="viewFiles">
          {{ $t('dilution-management.solution-preparation.view') }}
        </Button>
        <Button type="primary" @click="batchAddFiles">
          {{ $t('dilution-management.solution-preparation.batchAdd') }}
        </Button>
      </Space>
    </template>
  </FileGrid>
</template>
