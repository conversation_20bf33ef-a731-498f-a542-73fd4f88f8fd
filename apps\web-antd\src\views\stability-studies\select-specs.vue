<script lang="ts" setup>
import type { Recordable } from '@vben/types';

import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Button, message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import {
  $addSpecToTemplateApi,
  $getSpecForObjectByIdApi,
} from '#/api/business-static-tables/process-specifications';
import { $getCboSpecsApi, $setSpecDataApi } from '#/api/stability-studies';
import { $t } from '#/locales';
import AddApecificationForm from '#/views/business-static-tables/process-specifications/add-specification.vue';

const emit = defineEmits(['success']);

interface FormArg {
  nStabno: number;
  nSpcode: number;
  sOrigCurr: string;
  nSpecO: number;
}
const formArg = ref<FormArg>();
const [Modal, modalApi] = useVbenModal({
  destroyOnClose: true,
  showConfirmButton: true,
  confirmDisabled: false,
  onConfirm: async () => {
    const { valid } = await formApi.validate();
    if (!valid) return;
    if (!formArg.value) {
      console.error('formArg is undefined');
      return;
    }
    const values = await formApi.getValues();
    if (values.specification < 1) {
      message.warning($t('stability-studies.ui.specificationIsNull'));
      return;
    }
    await $setSpecDataApi({
      stabNo: formArg.value.nStabno,
      origCurr: values.specToUse,
      specNo: values.specification,
    });
    emit('success');
    modalApi.close();
  },
  onOpenChange: async (isOpen) => {
    if (isOpen) {
      const data = modalApi.getData<FormArg>();
      if (data) {
        modalApi.lock();
        formArg.value = data;
        await loadSpecOptions();
        formApi.setValues({
          specification: data.nSpecO,
          specToUse: data.sOrigCurr,
        });
        modalApi.unlock();
      }
    }
  },
  onCancel: () => {
    modalApi.close();
  },
});
const [AddSpecModal, addSpecModalApi] = useVbenModal({
  connectedComponent: AddApecificationForm,
  destroyOnClose: true,
});

const [Form, formApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
  },
  layout: 'horizontal',
  resetButtonOptions: {
    show: false,
  },
  schema: [
    {
      component: 'Select',
      componentProps: {
        showSearch: true,
        filterOption: (input: string, option: Recordable<any>) => {
          return option.label.toLowerCase().includes(input.toLowerCase());
        },
        options: [],
      },
      fieldName: 'specification',
      label: $t('stability-studies.columns.specification'),
      rules: 'selectRequired',
    },
    {
      component: 'Select',
      componentProps: {
        options: [
          {
            label: 'O - 保持所选版本',
            value: 'O',
          },
          {
            label: 'C - 使用最新版本',
            value: 'C',
          },
        ],
        showSearch: true,
        filterOption: (input: string, option: Recordable<any>) => {
          return option.label.toLowerCase().includes(input.toLowerCase());
        },
      },
      fieldName: 'specToUse',
      label: $t('stability-studies.columns.specToUse'),
      rules: 'selectRequired',
    },
  ],
  submitButtonOptions: {
    show: false,
  },
  wrapperClass: 'grid-cols-1 md:grid-cols-1 lg:grid-cols-1',
});

async function loadSpecOptions() {
  const data = formArg.value;
  if (!data) return;
  const specOptions = await $getSpecForObjectByIdApi({
    sTableName: 'SP_SPECS',
    sKeyFieldName: 'SP_CODE',
    nValueKey: data.nSpcode,
    aAdHocSpecs: [],
    bFilterSeason: false,
  });
  const aSpecno = specOptions.map((item: any[]) => item[0]);
  if (aSpecno.length > 0) {
    const newSpecOptions = await $getCboSpecsApi({
      prmDt: aSpecno.join(','),
    });
    formApi.updateSchema([
      {
        fieldName: 'specification',
        componentProps: {
          options: newSpecOptions.map((item) => ({
            label: item.TEXT,
            value: item.VALUE,
          })),
        },
      },
    ]);
  }
}
const tableName = 'SP_SPECS';
const keyField = 'SP_CODE';
const folderNo = '';
function onCreate() {
  addSpecModalApi
    .setData({
      MODE: 'ADD',
      sTable: tableName,
      FieldValue: formArg.value?.nSpcode,
    })
    .open();
}
async function addSpecToTemplate(specNo: number) {
  if (!formArg.value) {
    console.error('formArg is undefined');
    return;
  }
  await $addSpecToTemplateApi({
    tableName,
    fieldValue: formArg.value.nSpcode,
    specNoArr: [specNo],
    keyField,
    folderNo,
  });
  await loadSpecOptions();
}
</script>

<template>
  <Modal :title="$t('stability-studies.ui.selectSpec')">
    <AddSpecModal @success="addSpecToTemplate($event)" />
    <div style="padding: 5px 0 0 30px">
      <Button type="primary" @click="onCreate">
        {{ $t('ui.actionTitle.create') }}
      </Button>
    </div>
    <Form class="p-10" />
  </Modal>
</template>
