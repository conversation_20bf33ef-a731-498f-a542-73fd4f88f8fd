# 会话监控功能模块

基于 sessions-swagger.json API 文档开发的完整会话监控功能模块。

## 功能特性

### 1. 会话统计仪表板 (dashboard.vue)
- ✅ 实时显示在线用户数、活跃会话数等统计信息
- ✅ 设备类型分布可视化展示
- ✅ 自动刷新数据（每30秒）
- ✅ 响应式布局设计

### 2. 活跃会话列表 (list.vue)
- ✅ 分页展示所有活跃会话
- ✅ 支持按用户名、IP地址、设备类型筛选
- ✅ 显示详细会话信息：用户名、登录时间、设备信息等
- ✅ 强制下线单个会话功能
- ✅ 查看会话详情功能

### 3. 用户会话管理 (user-sessions.vue)
- ✅ 用户选择器，支持搜索
- ✅ 显示选中用户的所有活跃会话
- ✅ 批量强制下线用户所有会话
- ✅ 单个会话强制下线

### 4. 会话详情组件 (modules/session-detail.vue)
- ✅ 模态框展示会话详细信息
- ✅ 分类显示：基本信息、设备信息、登录信息
- ✅ 美观的卡片布局设计

## 技术实现

### API 接口
- `GET /api/platform/sessions/active` - 获取活跃会话列表
- `GET /api/platform/sessions/statistics` - 获取会话统计信息
- `GET /api/platform/sessions/user/{userId}` - 获取指定用户会话
- `POST /api/platform/sessions/{sessionId}/force-logout` - 强制下线指定会话
- `POST /api/platform/sessions/user/{userId}/force-logout-all` - 强制下线用户所有会话

### 技术栈
- **Vue3 + TypeScript + 组合式API**
- **Ant Design Vue** 组件库
- **lucide-vue-next** 图标库
- **VxeGrid** 表格组件
- **dayjs** 时间处理

### 文件结构
```
/apps/web-antd/src/views/system/sessions/
├── index.vue                    # 主入口页面（Tab切换）
├── list.vue                     # 活跃会话列表页面
├── dashboard.vue                # 会话统计仪表板
├── user-sessions.vue            # 用户会话管理页面
├── data.ts                      # 表格列定义和表单配置
└── modules/
    └── session-detail.vue       # 会话详情弹窗组件
```

### 类型定义
```
/apps/web-antd/src/types/platform/
└── sessions.ts                  # 会话相关TypeScript类型定义
```

### API 服务
```
/apps/web-antd/src/api/platform/
└── sessions.ts                  # 会话管理API接口
```

## 使用方法

### 1. 访问页面
导航到 **系统管理 > 会话管理** 菜单

### 2. 功能操作

#### 会话统计仪表板
- 查看实时统计数据
- 观察设备类型分布
- 监控系统活跃度

#### 活跃会话列表
- 使用搜索框筛选会话
- 点击"强制下线"按钮下线会话
- 点击"详情"查看会话详细信息

#### 用户会话管理
- 选择用户查看其所有会话
- 使用"下线所有会话"批量操作
- 单独管理用户的每个会话

### 3. 权限控制
- 需要相应的系统管理权限
- 强制下线操作需要管理员权限

## 国际化支持

已添加中英文翻译支持：
- 中文：`/apps/web-antd/src/locales/langs/zh-CN/system.json`
- 英文：`/apps/web-antd/src/locales/langs/en-US/system.json`

## 错误处理

- API 请求失败时显示友好错误提示
- 网络异常时自动重试机制
- 操作确认对话框防止误操作

## 性能优化

- 表格数据分页加载
- 统计数据定时刷新
- 组件按需加载
- 响应式设计适配移动端

## 扩展功能

可以进一步扩展的功能：
- 会话活动日志记录
- 会话地理位置展示
- 异常登录检测和告警
- 会话时长统计分析
- 导出会话报表

## 注意事项

1. 确保后端 API 服务正常运行
2. 检查用户权限配置
3. 定期清理过期会话数据
4. 监控系统性能影响
