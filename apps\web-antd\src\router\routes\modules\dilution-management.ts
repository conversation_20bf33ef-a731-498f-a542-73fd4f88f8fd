import type { RouteRecordRaw } from 'vue-router';

import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'ic:baseline-view-in-ar',
      keepAlive: true,
      order: 1000,
      title: $t('dilution-management.title'),
    },
    name: 'Dilution-management',
    path: '/dilution-management',
    children: [
      {
        meta: {
          title: $t('dilution-management.solution-preparation.title'),
          icon: 'ic:baseline-view-in-ar',
        },
        name: 'solution-preparation',
        path: '/dilution-management/solution-preparation',
        component: () =>
          import(
            '#/views/dilution-management/solution-preparation/solution-preparation.vue'
          ),
      },
      {
        meta: {
          title: $t('dilution-management.solution-preparation-audit.title'),
          icon: 'ic:baseline-view-in-ar',
        },
        name: 'solution-preparation-audit',
        path: '/dilution-management/solution-preparation-audit',
        component: () =>
          import(
            '#/views/dilution-management/solution-preparation-audit/solution-preparation-audit.vue'
          ),
      },
      {
        meta: {
          title: $t('dilution-management.solution-preparation-store.title'),
          icon: 'ic:baseline-view-in-ar',
        },
        name: 'solution-preparation-store',
        path: '/dilution-management/solution-preparation-store',
        component: () =>
          import(
            '#/views/dilution-management/solution-preparation-store/solution-preparation-store.vue'
          ),
      },
      {
        meta: {
          title: $t('dilution-management.solution-preparation-search.title'),
          icon: 'ic:baseline-view-in-ar',
        },
        name: 'solution-preparation-search',
        path: '/dilution-management/solution-preparation-search',
        component: () =>
          import(
            '#/views/dilution-management/solution-preparation-search/solution-preparation-search.vue'
          ),
      },
    ],
  },
];
export default routes;
