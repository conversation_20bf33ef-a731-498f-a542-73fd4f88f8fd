/**
 * 会话管理相关类型定义
 */

/** 用户会话DTO */
export interface UserSessionDTO {
  /** 会话ID */
  id: string;
  /** 用户ID */
  userId: string;
  /** 用户名 */
  userName: string;
  /** 令牌ID */
  tokenId: string;
  /** 登录时间 */
  loginTime: string;
  /** 最后活动时间 */
  lastActivityTime: string;
  /** 令牌过期时间 */
  tokenExpiresAt: string;
  /** IP地址 */
  ipAddress: string;
  /** 用户代理 */
  userAgent: string;
  /** 设备类型 */
  deviceType: string;
  /** 操作系统 */
  operatingSystem: string;
  /** 浏览器 */
  browser: string;
  /** 位置信息 */
  location?: string;
  /** 状态 */
  status: string;
  /** 登录源 */
  loginSource: string;
  /** 是否活跃 */
  isActive: boolean;
}

/** 获取活跃会话响应 */
export interface GetActiveSessionsResponse {
  /** 会话列表 */
  sessions: UserSessionDTO[];
  /** 总数量 */
  totalCount: number;
  /** 页码 */
  pageNumber: number;
  /** 页大小 */
  pageSize: number;
  /** 总页数 */
  totalPages: number;
}

/** 会话统计DTO */
export interface SessionStatisticsDTO {
  /** 在线用户总数 */
  totalOnlineUsers: number;
  /** 活跃会话总数 */
  totalActiveSessions: number;
  /** Web会话数 */
  webSessions: number;
  /** 移动端会话数 */
  mobileSessions: number;
  /** 桌面端会话数 */
  desktopSessions: number;
  /** API会话数 */
  apiSessions: number;
  /** 今日新增会话数 */
  todayNewSessions: number;
  /** 统计时间 */
  statisticsTime: string;
}

/** 获取用户会话响应 */
export interface GetUserSessionsResponse {
  /** 会话列表 */
  sessions: UserSessionDTO[];
}

/** 强制下线请求 */
export interface ForceLogoutRequest {
  /** 下线原因 */
  reason?: string;
}

/** 强制下线用户所有会话请求 */
export interface ForceLogoutAllUserSessionsRequest {
  /** 下线原因 */
  reason?: string;
}

/** 获取活跃会话请求参数 */
export interface GetActiveSessionsParams {
  /** 页码 */
  pageNumber: number;
  /** 页大小 */
  pageSize: number;
  /** 用户名筛选 */
  userName?: string;
  /** IP地址筛选 */
  ipAddress?: string;
  /** 设备类型筛选 */
  deviceType?: string;
}

/** 错误响应 */
export interface ErrorResponse {
  /** HTTP状态码 */
  statusCode: number;
  /** 错误消息 */
  message: string;
  /** 错误详情 */
  errors?: Record<string, string[]>;
}

/** 设备类型枚举 */
export enum DeviceType {
  API = 'API',
  DESKTOP = 'Desktop',
  MOBILE = 'Mobile',
  WEB = 'Web',
}

/** 会话状态枚举 */
export enum SessionStatus {
  ACTIVE = 'Active',
  EXPIRED = 'Expired',
  INACTIVE = 'Inactive',
}

/** 登录源枚举 */
export enum LoginSource {
  API = 'API',
  MOBILE = 'Mobile',
  SSO = 'SSO',
  WEB = 'Web',
}
