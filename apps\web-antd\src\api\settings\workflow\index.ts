import type { WitLabStep } from '#/components/logic-flow/types/workflow';

import { callServer, getDataSetNoPage } from '#/api/core';

export async function getWorkflows() {
  return getDataSetNoPage(
    'GeneralWorkFlowManager.WORKFLOWTEMPLATES_GD_NEW',
    [],
  );
}

export async function updateWorkflowStep(updateWorkflowStep: WitLabStep) {
  const {
    ORIGREC,
    SORTER,
    STEPSTATUS,
    STEPDISPSTATUS,
    STEPNAME,
    SIGNATURETYPE,
    COMMENTNAME,
    MENUS,
    PERMISSIONS,
  } = updateWorkflowStep;
  const fields: Record<string, any> = {
    SORTER,
    STEPSTATUS,
    STEPDISPSTATUS,
    STEPNAME,
    SIGNATURETYPE,
    COMMENTNAME,
  };
  const fieldUpdates = Object.keys(fields).map((field) =>
    callServer('Common.Update', [
      'GENERAL_WORKFLOW_STEPS',
      field,
      fields[field],
      ORIGREC,
    ]),
  );

  try {
    await Promise.all([
      ...fieldUpdates,
      callServer(
        'GeneralWorkFlowManager.UpdateWorkflowStepMenus.UpdateStepMenus',
        [ORIGREC, MENUS],
      ),
      callServer(
        'GeneralWorkFlowManager.UpdateWorkflowStepMenus.UpdateStepPermissions',
        [ORIGREC, PERMISSIONS],
      ),
    ]);
  } catch (error) {
    console.error(error);
  }
}

export async function getWorkflowPermissionsByMenu(
  menuName: string,
): Promise<string[]> {
  return callServer(
    'GeneralWorkFlowManager.GetWorkflowStepMenus.GetWorkflowPermissionsByMenu',
    [menuName],
  );
}

export async function getWorkflowByMenu(menuName: string): Promise<string[]> {
  return callServer(
    'GeneralWorkFlowManager.GetWorkflowStepMenus.GetWorkflowByMenu',
    [menuName],
  );
}
