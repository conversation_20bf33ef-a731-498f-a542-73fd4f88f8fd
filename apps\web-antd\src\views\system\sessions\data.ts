import type { VbenFormSchema } from '#/adapter/form';
import type { OnActionClickFn, VxeTableGridOptions } from '#/adapter/vxe-table';
import type { UserSessionDTO } from '#/types/platform/sessions';

import dayjs from 'dayjs';

import { z } from '#/adapter/form';
import { $t } from '#/locales';

/**
 * 活跃会话列表表格列配置
 */
export function useSessionColumns(onActionClick: OnActionClickFn) {
  return [
    {
      field: 'userName',
      title: $t('system.sessions.userName'),
      width: 120,
      fixed: 'left',
    },
    {
      field: 'ipAddress',
      title: $t('system.sessions.ipAddress'),
      width: 140,
    },
    {
      field: 'deviceType',
      title: $t('system.sessions.deviceType'),
      width: 100,
      cellRender: {
        name: 'VxeTag',
        props: ({ row }: { row: UserSessionDTO }) => {
          const colorMap = {
            Web: 'blue',
            Mobile: 'green',
            Desktop: 'orange',
            API: 'purple',
          };
          return {
            color: colorMap[row.deviceType as keyof typeof colorMap] || 'default',
            content: row.deviceType,
          };
        },
      },
    },
    {
      field: 'operatingSystem',
      title: $t('system.sessions.operatingSystem'),
      width: 120,
    },
    {
      field: 'browser',
      title: $t('system.sessions.browser'),
      width: 120,
    },
    {
      field: 'loginTime',
      title: $t('system.sessions.loginTime'),
      width: 160,
      formatter: ({ cellValue }: { cellValue: string }) => {
        return cellValue ? dayjs(cellValue).format('YYYY-MM-DD HH:mm:ss') : '-';
      },
    },
    {
      field: 'lastActivityTime',
      title: $t('system.sessions.lastActivityTime'),
      width: 160,
      formatter: ({ cellValue }: { cellValue: string }) => {
        return cellValue ? dayjs(cellValue).format('YYYY-MM-DD HH:mm:ss') : '-';
      },
    },
    {
      field: 'status',
      title: $t('system.sessions.status'),
      width: 100,
      cellRender: {
        name: 'VxeTag',
        props: ({ row }: { row: UserSessionDTO }) => {
          const colorMap = {
            Active: 'success',
            Inactive: 'warning',
            Expired: 'error',
          };
          return {
            color: colorMap[row.status as keyof typeof colorMap] || 'default',
            content: row.status,
          };
        },
      },
    },
    {
      field: 'location',
      title: $t('system.sessions.location'),
      width: 120,
      formatter: ({ cellValue }: { cellValue: string }) => {
        return cellValue || '-';
      },
    },
    {
      field: 'actions',
      title: '操作',
      width: 120,
      fixed: 'right',
      cellRender: {
        name: 'VxeButtonGroup',
        children: [
          {
            props: {
              content: $t('system.sessions.forceLogout'),
              type: 'text',
              status: 'danger',
              size: 'mini',
            },
            events: {
              click: ({ row }: { row: UserSessionDTO }) => {
                onActionClick({ code: 'forceLogout', row });
              },
            },
          },
          {
            props: {
              content: '详情',
              type: 'text',
              size: 'mini',
            },
            events: {
              click: ({ row }: { row: UserSessionDTO }) => {
                onActionClick({ code: 'detail', row });
              },
            },
          },
        ],
      },
    },
  ];
}

/**
 * 活跃会话搜索表单配置
 */
export function useSessionFilterSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      componentProps: {
        placeholder: $t('system.sessions.filter.userNamePlaceholder'),
        clearable: true,
      },
      fieldName: 'userName',
      label: $t('system.sessions.userName'),
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: $t('system.sessions.filter.ipAddressPlaceholder'),
        clearable: true,
      },
      fieldName: 'ipAddress',
      label: $t('system.sessions.ipAddress'),
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: $t('system.sessions.filter.deviceTypePlaceholder'),
        clearable: true,
        options: [
          { label: $t('system.sessions.filter.all'), value: '' },
          { label: $t('system.sessions.filter.web'), value: 'Web' },
          { label: $t('system.sessions.filter.mobile'), value: 'Mobile' },
          { label: $t('system.sessions.filter.desktop'), value: 'Desktop' },
          { label: $t('system.sessions.filter.api'), value: 'API' },
        ],
      },
      fieldName: 'deviceType',
      label: $t('system.sessions.deviceType'),
    },
  ];
}

/**
 * 强制下线表单配置
 */
export function useForceLogoutFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Textarea',
      componentProps: {
        placeholder: $t('system.sessions.reasonPlaceholder'),
        rows: 3,
        maxlength: 200,
        showCount: true,
      },
      fieldName: 'reason',
      label: $t('system.sessions.reason'),
      rules: z.string().max(200).optional(),
    },
  ];
}

/**
 * 用户会话列表表格列配置
 */
export function useUserSessionColumns(onActionClick: OnActionClickFn) {
  return [
    {
      field: 'deviceType',
      title: $t('system.sessions.deviceType'),
      width: 100,
      cellRender: {
        name: 'VxeTag',
        props: ({ row }: { row: UserSessionDTO }) => {
          const colorMap = {
            Web: 'blue',
            Mobile: 'green',
            Desktop: 'orange',
            API: 'purple',
          };
          return {
            color: colorMap[row.deviceType as keyof typeof colorMap] || 'default',
            content: row.deviceType,
          };
        },
      },
    },
    {
      field: 'ipAddress',
      title: $t('system.sessions.ipAddress'),
      width: 140,
    },
    {
      field: 'operatingSystem',
      title: $t('system.sessions.operatingSystem'),
      width: 120,
    },
    {
      field: 'browser',
      title: $t('system.sessions.browser'),
      width: 120,
    },
    {
      field: 'loginTime',
      title: $t('system.sessions.loginTime'),
      width: 160,
      formatter: ({ cellValue }: { cellValue: string }) => {
        return cellValue ? dayjs(cellValue).format('YYYY-MM-DD HH:mm:ss') : '-';
      },
    },
    {
      field: 'lastActivityTime',
      title: $t('system.sessions.lastActivityTime'),
      width: 160,
      formatter: ({ cellValue }: { cellValue: string }) => {
        return cellValue ? dayjs(cellValue).format('YYYY-MM-DD HH:mm:ss') : '-';
      },
    },
    {
      field: 'status',
      title: $t('system.sessions.status'),
      width: 100,
      cellRender: {
        name: 'VxeTag',
        props: ({ row }: { row: UserSessionDTO }) => {
          const colorMap = {
            Active: 'success',
            Inactive: 'warning',
            Expired: 'error',
          };
          return {
            color: colorMap[row.status as keyof typeof colorMap] || 'default',
            content: row.status,
          };
        },
      },
    },
    {
      field: 'actions',
      title: '操作',
      width: 120,
      fixed: 'right',
      cellRender: {
        name: 'VxeButtonGroup',
        children: [
          {
            props: {
              content: $t('system.sessions.forceLogout'),
              type: 'text',
              status: 'danger',
              size: 'mini',
            },
            events: {
              click: ({ row }: { row: UserSessionDTO }) => {
                onActionClick({ code: 'forceLogout', row });
              },
            },
          },
          {
            props: {
              content: '详情',
              type: 'text',
              size: 'mini',
            },
            events: {
              click: ({ row }: { row: UserSessionDTO }) => {
                onActionClick({ code: 'detail', row });
              },
            },
          },
        ],
      },
    },
  ];
}
