<script lang="ts" setup>
import { ref } from 'vue';

import { Page } from '@vben/common-ui';

import { Card, Col, Row, Tabs, TabPane } from 'ant-design-vue';
import { Activity, BarChart3, Users } from 'lucide-vue-next';

import { $t } from '#/locales';

import Dashboard from './dashboard.vue';
import List from './list.vue';
import UserSessions from './user-sessions.vue';

defineOptions({
  name: 'SessionsIndex',
});

const activeTab = ref('dashboard');
</script>

<template>
  <Page auto-content-height>
    <div class="p-4">
      <Card>
        <Tabs v-model:activeKey="activeTab" type="card">
          <TabPane key="dashboard" :tab="$t('system.sessions.dashboard')">
            <template #tab>
              <span class="flex items-center space-x-2">
                <BarChart3 class="size-4" />
                <span>{{ $t('system.sessions.dashboard') }}</span>
              </span>
            </template>
            <Dashboard />
          </TabPane>
          
          <TabPane key="list" :tab="$t('system.sessions.list')">
            <template #tab>
              <span class="flex items-center space-x-2">
                <Activity class="size-4" />
                <span>{{ $t('system.sessions.list') }}</span>
              </span>
            </template>
            <List />
          </TabPane>
          
          <TabPane key="user-sessions" :tab="$t('system.sessions.userSessions')">
            <template #tab>
              <span class="flex items-center space-x-2">
                <Users class="size-4" />
                <span>{{ $t('system.sessions.userSessions') }}</span>
              </span>
            </template>
            <UserSessions />
          </TabPane>
        </Tabs>
      </Card>
    </div>
  </Page>
</template>

<style scoped>
:deep(.ant-tabs-content-holder) {
  padding: 0;
}

:deep(.ant-tabs-tabpane) {
  padding: 0;
}
</style>
