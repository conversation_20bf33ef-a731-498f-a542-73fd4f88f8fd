import type {
  ForceLogoutAllUserSessionsRequest,
  ForceLogoutRequest,
  GetActiveSessionsParams,
  GetActiveSessionsResponse,
  GetUserSessionsResponse,
  SessionStatisticsDTO,
} from '#/types/platform/sessions';

import { backendRequestClient } from '#/api/request';

/**
 * 会话管理API
 */
export namespace SessionsApi {
  /** API基础路径 */
  const BASE_PATH = '/api/platform/sessions';

  /**
   * 获取活跃会话列表
   * @param params 查询参数
   * @returns 活跃会话响应
   */
  export async function getActiveSessions(
    params: GetActiveSessionsParams,
  ): Promise<GetActiveSessionsResponse> {
    return backendRequestClient.get(`${BASE_PATH}/active`, {
      params: {
        pageNumber: params.pageNumber,
        pageSize: params.pageSize,
        ...(params.userName && { userName: params.userName }),
        ...(params.ipAddress && { ipAddress: params.ipAddress }),
        ...(params.deviceType && { deviceType: params.deviceType }),
      },
    });
  }

  /**
   * 获取会话统计信息
   * @returns 会话统计数据
   */
  export async function getSessionStatistics(): Promise<SessionStatisticsDTO> {
    return backendRequestClient.get(`${BASE_PATH}/statistics`);
  }

  /**
   * 获取指定用户的会话列表
   * @param userId 用户ID
   * @returns 用户会话响应
   */
  export async function getUserSessions(
    userId: string,
  ): Promise<GetUserSessionsResponse> {
    return backendRequestClient.get(`${BASE_PATH}/user/${userId}`);
  }

  /**
   * 强制下线指定会话
   * @param sessionId 会话ID
   * @param request 强制下线请求
   * @returns 操作结果
   */
  export async function forceLogoutSession(
    sessionId: string,
    request: ForceLogoutRequest = {},
  ): Promise<void> {
    return backendRequestClient.post(
      `${BASE_PATH}/${sessionId}/force-logout`,
      request,
    );
  }

  /**
   * 强制下线用户所有会话
   * @param userId 用户ID
   * @param request 强制下线请求
   * @returns 操作结果
   */
  export async function forceLogoutAllUserSessions(
    userId: string,
    request: ForceLogoutAllUserSessionsRequest = {},
  ): Promise<void> {
    return backendRequestClient.post(
      `${BASE_PATH}/user/${userId}/force-logout-all`,
      request,
    );
  }
}

// 导出API方法以便直接使用
export const {
  getActiveSessions,
  getSessionStatistics,
  getUserSessions,
  forceLogoutSession,
  forceLogoutAllUserSessions,
} = SessionsApi;
