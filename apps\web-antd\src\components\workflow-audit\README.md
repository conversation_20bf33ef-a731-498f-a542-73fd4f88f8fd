# WorkflowAudit 工作流审批组件

工作流审批组件提供了一个弹出式界面，用于选择工作流的下一个步骤并进行审批操作。该组件参考了电子签名组件的设计模式，提供了一致的用户体验。

## ✨ 特性

- 🕒 **实时时间显示** - 显示当前操作时间
- 🔐 **电子签名支持** - 可配置的电子签名验证
- 📋 **步骤选择** - 从可选的下一步骤中进行选择
- 💬 **备注功能** - 支持添加审批备注
- 🌐 **国际化支持** - 支持中英文界面
- 📱 **响应式设计** - 适配不同屏幕尺寸

## 🚀 快速开始

### 基础用法

```typescript
import { workflowAudit } from '#/components/workflow-audit';

// 简单调用
const result = await workflowAudit({
  workflowCode: 'BATCH_WORKFLOW',
  stepCode: 'DRAFT',
});

if (result) {
  console.log('选择的下一步骤:', result.nextStepName);
  console.log('步骤代码:', result.nextStepCode);
  console.log('备注:', result.comment);
}
```

### 高级用法

```typescript
import { workflowAudit } from '#/components/workflow-audit';

// 带自定义配置的调用
const result = await workflowAudit({
  workflowCode: 'QC_WORKFLOW',
  stepCode: 'TESTING',
  title: '质控工作流审批',
  width: '70vw',
});

if (result) {
  // 处理审批结果
  const { nextStepName, nextStepCode, comment, esigData } = result;

  // 如果有电子签名数据
  if (esigData?.hasEsig) {
    console.log('操作人:', esigData.username);
    console.log('操作时间:', esigData.timestamp.format('YYYY-MM-DD HH:mm:ss'));
  }

  // 提交到后端
  await submitWorkflowStep({
    nextStepCode,
    comment,
    esigData,
  });
} else {
  console.log('用户取消了操作');
}
```

## 📝 API 参考

### workflowAudit(options)

显示工作流审批对话框。

#### 参数

| 参数         | 类型               | 必填 | 默认值       | 说明         |
| ------------ | ------------------ | ---- | ------------ | ------------ |
| workflowCode | `string`           | ✓    | -            | 工作流代码   |
| stepCode     | `string`           | ✓    | -            | 当前步骤代码 |
| title        | `string`           | ✗    | '工作流审批' | 对话框标题   |
| width        | `number \| string` | ✗    | '60vw'       | 对话框宽度   |

#### 返回值

返回 `Promise<WorkflowAuditResult | null>`

**WorkflowAuditResult:**

```typescript
interface WorkflowAuditResult {
  nextStepName: string; // 选择的下一个步骤名称
  nextStepCode: string; // 选择的下一个步骤代码
  comment: string; // 备注内容
  esigData?: {
    // 电子签名数据（如果需要）
    username: string;
    timestamp: Dayjs;
    hasEsig: boolean;
  };
}
```

### workflowAuditEnd()

手动关闭工作流审批对话框。

```typescript
import { workflowAuditEnd } from '#/components/workflow-audit';

// 在需要的地方关闭对话框
workflowAuditEnd();
```

## 🔧 配置说明

组件会自动从服务器获取配置信息，如果服务器接口不可用，会使用内置的模拟数据：

### 电子签名配置

- 包含 'APPROVAL'、'REVIEW'、'RELEASE'、'AUTHORIZE' 的步骤会启用电子签名
- 其他步骤默认不需要电子签名

### 模拟数据

内置了以下工作流的模拟数据：

**BATCH_WORKFLOW（批次工作流）:**

- APPROVAL - 审批
- REVIEW - 审核
- RELEASE - 批准
- COMPLETE - 完成

**QC_WORKFLOW（质控工作流）:**

- TEST - 检测
- RESULT_REVIEW - 结果审核
- APPROVAL - 审批
- RELEASE - 发布

**DEVIATION_WORKFLOW（偏差工作流）:**

- INVESTIGATE - 调查
- CAPA - 纠正预防措施
- APPROVAL - 审批
- CLOSE - 关闭

## 🎨 界面展示

工作流审批对话框包含以下区域：

1. **当前时间** - 实时更新的操作时间
2. **电子签名** - 用户名和密码输入（根据配置显示）
3. **步骤选择** - 单选按钮组，选择下一个步骤
4. **备注** - 文本域，输入审批备注
5. **确认信息** - 显示选择的步骤和操作信息
6. **操作按钮** - 取消和确认按钮

## 🔍 错误处理

```typescript
try {
  const result = await workflowAudit({
    workflowCode: 'INVALID_WORKFLOW',
    stepCode: 'INVALID_STEP',
  });
} catch (error) {
  console.error('工作流审批失败:', error.message);

  if (error.message === 'No available next steps found') {
    // 处理没有可用步骤的情况
    message.warning('当前步骤没有可用的下一步骤');
  }
}
```

## 🌐 国际化

组件支持中英文界面，翻译键位于：

- `apps/web-antd/src/locales/langs/zh-CN/components.json`
- `apps/web-antd/src/locales/langs/en-US/components.json`

所有翻译键都在 `workflowAudit` 命名空间下。

## 💡 最佳实践

1. **错误处理**: 始终使用 try-catch 包裹调用
2. **结果检查**: 检查返回结果是否为 null（用户取消）
3. **数据验证**: 在使用结果数据前进行必要的验证
4. **用户体验**: 在长时间操作前显示加载状态

```typescript
// 推荐的使用模式
async function handleWorkflowAudit() {
  try {
    // 显示加载状态
    const loading = message.loading('正在加载工作流信息...', 0);

    const result = await workflowAudit({
      workflowCode: 'BATCH_WORKFLOW',
      stepCode: 'DRAFT',
    });

    loading();

    if (result) {
      // 验证数据
      if (!result.nextStepCode) {
        message.error('无效的步骤选择');
        return;
      }

      // 处理结果
      await processAuditResult(result);
      message.success('工作流审批成功');
    } else {
      message.info('操作已取消');
    }
  } catch (error) {
    console.error('工作流审批失败:', error);
    message.error('工作流审批失败，请重试');
  }
}
```

## 🔗 相关组件

- [电子签名组件](../esig/README.md) - 本组件的设计参考
- [工作流设计器](../logic-flow/README.md) - 工作流可视化设计
