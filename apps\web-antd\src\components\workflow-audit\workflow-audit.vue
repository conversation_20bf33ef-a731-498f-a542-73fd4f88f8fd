<script setup lang="ts">
import type { RadioChangeEvent } from 'ant-design-vue';

import type {
  WorkflowAuditConfig,
  WorkflowAuditFormData,
  WorkflowAuditOptions,
  WorkflowAuditResult,
} from './types';

import { computed, onMounted, reactive, ref } from 'vue';

import {
  Button,
  Card,
  Form,
  Input,
  message,
  Radio,
  Space,
} from 'ant-design-vue';
import dayjs from 'dayjs';

import { callServer, validateUserPassword } from '#/api';
import { $t } from '#/locales';

export interface WorkflowAuditComponentProps {
  config: WorkflowAuditConfig & WorkflowAuditOptions;
}

const props = defineProps<WorkflowAuditComponentProps>();

const emit = defineEmits<{
  cancel: [];
  confirm: [data: WorkflowAuditResult];
}>();

const formData = reactive<WorkflowAuditFormData>({
  username: props.config.username || '',
  password: '',
  nextStepCode:
    props.config.nextSteps?.length > 0
      ? props.config.nextSteps[0]?.stepCode || ''
      : '',
  comment: '',
});

const requiredComment = ref(false);

// 计算属性
const selectedStep = computed(() => {
  return props.config.nextSteps.find(
    (step) => step.stepCode === formData.nextStepCode,
  );
});

const canConfirm = computed(() => {
  let valid = Boolean(formData.nextStepCode);

  // 如果需要电子签名，检查用户名和密码
  if (props.config.esign) {
    valid = valid && Boolean(formData.username && formData.password);
  }

  return valid;
});

// 方法
const handleStepChange = async (e: RadioChangeEvent) => {
  const nextStepCode = e.target.value;
  const [_requiredComment] = await callServer(
    'GeneralWorkFlowActions.GetWFStepDet',
    [props.config.workflowCode, props.config.stepCode, nextStepCode],
  );
  requiredComment.value = _requiredComment === 'Y';
};

const validateUser = async () => {
  if (!formData.username || !formData.password) {
    message.error($t('components.workflowAudit.username_password_required'));
    return false;
  }

  try {
    const validateResult = await validateUserPassword(
      formData.username,
      formData.password,
    );

    if (!validateResult) {
      message.error($t('components.workflowAudit.password_error'));
      return false;
    }
    return true;
  } catch {
    message.error($t('components.workflowAudit.password_validation_failed'));
    return false;
  }
};

const handleConfirm = async () => {
  // 验证表单
  if (!formData.nextStepCode) {
    message.error($t('components.workflowAudit.step_required'));
    return;
  }

  // 如果需要电子签名，验证用户密码
  if (props.config.esign) {
    const valid = await validateUser();
    if (!valid) {
      return;
    }
  }

  const selectedStepInfo = selectedStep.value;
  if (!selectedStepInfo) {
    message.error($t('components.workflowAudit.step_not_found'));
    return;
  }

  if (requiredComment.value && !formData.comment) {
    message.error($t('components.workflowAudit.comment_required'));
    return;
  }

  const result: WorkflowAuditResult = {
    nextStepName: selectedStepInfo.stepName,
    nextStepCode: selectedStepInfo.stepCode,
    comment: formData.comment,
  };

  // 如果有电子签名，添加相关信息
  if (props.config.esign) {
    result.esigData = {
      username: formData.username,
      timestamp: dayjs(),
      hasEsig: true,
    };
  }

  const [isSuccess, errorMessage] = await callServer(
    'GeneralWorkFlowActions.Audit',
    [
      props.config.workflowCode,
      props.config.stepCode,
      formData.nextStepCode,
      props.config.origRec,
      formData.comment,
      formData.username,
    ],
  );

  if (!isSuccess) {
    message.error(errorMessage || $t('components.workflowAudit.audit_failed'));
    return;
  }

  emit('confirm', result);
};

const handleCancel = () => {
  emit('cancel');
};

onMounted(() => {
  if (props.config.nextSteps.length > 0) {
    handleStepChange({
      target: { value: props.config.nextSteps?.[0]?.stepCode },
    } as RadioChangeEvent);
  }
});
</script>

<template>
  <div class="workflow-audit">
    <!-- 电子签名区域（如果需要） -->
    <Card
      v-if="config.esign"
      :title="$t('components.workflowAudit.electronic_signature')"
      class="mb-4"
      :head-style="{ fontSize: '16px', minHeight: '48px' }"
    >
      <Form layout="horizontal" label-align="right" :label-col="{ span: 4 }">
        <Form.Item :label="$t('components.workflowAudit.username')">
          <Input
            v-model:value="formData.username"
            :placeholder="$t('components.workflowAudit.username_placeholder')"
            style="width: 280px"
          />
        </Form.Item>
        <Form.Item :label="$t('components.workflowAudit.password')">
          <Input.Password
            v-model:value="formData.password"
            :placeholder="$t('components.workflowAudit.password_placeholder')"
            style="width: 280px"
          />
        </Form.Item>
      </Form>
    </Card>

    <!-- 工作流步骤选择 -->
    <Card
      :title="$t('components.workflowAudit.next_step')"
      class="mb-4"
      :head-style="{ fontSize: '16px', minHeight: '48px' }"
    >
      <Radio.Group
        v-model:value="formData.nextStepCode"
        class="step-radio-group"
        @change="handleStepChange"
      >
        <div
          v-for="step in config.nextSteps"
          :key="step.stepCode"
          class="step-option"
        >
          <Radio :value="step.stepCode">
            <div class="step-info">
              <div class="step-name">{{ step.stepName }}</div>
              <div v-if="step.description" class="step-description">
                {{ step.description }}
              </div>
            </div>
          </Radio>
        </div>
      </Radio.Group>
    </Card>

    <!-- 备注区域 -->
    <Card
      :title="config.commentLabel || $t('components.workflowAudit.comment')"
      class="mb-4"
      :head-style="{ fontSize: '16px', minHeight: '48px' }"
    >
      <Input.TextArea
        v-model:value="formData.comment"
        :rows="4"
        :placeholder="$t('components.workflowAudit.comment_placeholder')"
        :required="requiredComment"
        :status="requiredComment && !formData.comment ? 'error' : undefined"
      />
    </Card>

    <!-- 操作按钮 -->
    <div class="footer-buttons">
      <Space>
        <Button @click="handleCancel">
          {{ $t('components.workflowAudit.cancel') }}
        </Button>
        <Button type="primary" @click="handleConfirm" :disabled="!canConfirm">
          {{ $t('components.workflowAudit.confirm') }}
        </Button>
      </Space>
    </div>
  </div>
</template>

<style scoped>
.workflow-audit {
  width: 100%;
  max-width: 800px;
  padding: 16px;
  margin: 0 auto;
}

.step-radio-group {
  width: 100%;
}

.step-option {
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.step-option:last-child {
  border-bottom: none;
}

.step-info {
  margin-left: 8px;
}

.step-name {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
}

.step-description {
  margin-top: 4px;
  font-size: 12px;
  color: #8c8c8c;
}

.footer-buttons {
  flex-shrink: 0;
  padding-top: 16px;
  text-align: right;
  border-top: 1px solid #f0f0f0;
}

.mb-4 {
  margin-bottom: 16px;
}
</style>
