<script lang="ts" setup>
import type {
  OnActionClickParams,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';
import type { UserSessionDTO } from '#/types/platform/sessions';

import { computed, ref, watch } from 'vue';

import { Page, useVbenForm, useVbenModal } from '@vben/common-ui';

import {
  Button,
  Card,
  Col,
  Empty,
  message,
  Row,
  Select,
  SelectOption,
  Space,
  Spin,
} from 'ant-design-vue';
import { LogOut, RefreshCw, User } from 'lucide-vue-next';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  forceLogoutAllUserSessions,
  forceLogoutSession,
  getUserSessions,
} from '#/api/platform/sessions';
import { getUserList } from '#/api/system/user';
import { $t } from '#/locales';

import {
  useForceLogoutFormSchema,
  useUserSessionColumns,
} from './data';
import SessionDetail from './modules/session-detail.vue';

defineOptions({
  name: 'UserSessions',
});

// 用户列表
const userList = ref<any[]>([]);
const loadingUsers = ref(false);
const selectedUserId = ref<string>('');
const selectedUserName = ref<string>('');

// 会话数据
const sessions = ref<UserSessionDTO[]>([]);
const loadingSessions = ref(false);

// 会话详情弹窗
const [SessionDetailModal, sessionDetailModalApi] = useVbenModal({
  connectedComponent: SessionDetail,
  destroyOnClose: true,
});

// 强制下线表单弹窗
const [ForceLogoutForm, forceLogoutFormApi] = useVbenForm({
  schema: useForceLogoutFormSchema(),
});

const [ForceLogoutModal, forceLogoutModalApi] = useVbenModal({
  connectedComponent: ForceLogoutForm,
  destroyOnClose: true,
  onConfirm: async () => {
    await handleForceLogout();
  },
});

// 批量下线表单弹窗
const [ForceLogoutAllModal, forceLogoutAllModalApi] = useVbenModal({
  connectedComponent: ForceLogoutForm,
  destroyOnClose: true,
  onConfirm: async () => {
    await handleForceLogoutAll();
  },
});

// 当前选中的会话
const currentSession = ref<UserSessionDTO | null>(null);

// 表格配置
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions: {
    columns: useUserSessionColumns(onActionClick),
    data: sessions,
    height: 'auto',
    keepSource: true,
    pagerConfig: {
      enabled: false,
    },
    rowConfig: {
      keyField: 'id',
    },
    toolbarConfig: {
      slots: {
        buttons: 'toolbarButtons',
      },
      custom: true,
      export: false,
      refresh: false,
      zoom: true,
    },
  } as VxeTableGridOptions,
});

// 计算属性
const sessionCount = computed(() => sessions.value.length);
const hasSelectedUser = computed(() => !!selectedUserId.value);
const hasSessions = computed(() => sessions.value.length > 0);

/**
 * 加载用户列表
 */
async function loadUsers() {
  loadingUsers.value = true;
  try {
    const response = await getUserList({
      page: 1,
      pageSize: 1000, // 获取所有用户
    });
    userList.value = response.items || [];
  } catch (error) {
    console.error('Failed to load users:', error);
    message.error('加载用户列表失败');
  } finally {
    loadingUsers.value = false;
  }
}

/**
 * 加载用户会话
 */
async function loadUserSessions() {
  if (!selectedUserId.value) {
    sessions.value = [];
    return;
  }

  loadingSessions.value = true;
  try {
    const response = await getUserSessions(selectedUserId.value);
    sessions.value = response.sessions || [];
    
    // 更新表格数据
    gridApi.setData(sessions.value);
  } catch (error) {
    console.error('Failed to load user sessions:', error);
    message.error('加载用户会话失败');
    sessions.value = [];
  } finally {
    loadingSessions.value = false;
  }
}

/**
 * 处理用户选择变化
 */
function onUserChange(userId: string) {
  selectedUserId.value = userId;
  const user = userList.value.find(u => u.id === userId);
  selectedUserName.value = user?.userName || '';
}

/**
 * 处理操作按钮点击
 */
function onActionClick({ code, row }: OnActionClickParams<UserSessionDTO>) {
  switch (code) {
    case 'forceLogout': {
      onForceLogout(row);
      break;
    }
    case 'detail': {
      onViewDetail(row);
      break;
    }
    default: {
      break;
    }
  }
}

/**
 * 查看会话详情
 */
function onViewDetail(session: UserSessionDTO) {
  sessionDetailModalApi.setData(session).open();
}

/**
 * 强制下线单个会话
 */
function onForceLogout(session: UserSessionDTO) {
  currentSession.value = session;
  forceLogoutModalApi
    .setData({ reason: '' })
    .setTitle($t('system.sessions.forceLogout'))
    .open();
}

/**
 * 强制下线所有会话
 */
function onForceLogoutAll() {
  if (!selectedUserId.value || !selectedUserName.value) return;
  
  forceLogoutAllModalApi
    .setData({ reason: '' })
    .setTitle($t('system.sessions.forceLogoutAll'))
    .open();
}

/**
 * 执行强制下线单个会话
 */
async function handleForceLogout() {
  if (!currentSession.value) return;

  try {
    const formData = forceLogoutFormApi.getValues();
    await forceLogoutSession(currentSession.value.id, {
      reason: formData.reason || undefined,
    });
    
    message.success($t('system.sessions.forceLogoutSuccess'));
    forceLogoutModalApi.close();
    loadUserSessions(); // 重新加载会话列表
  } catch (error) {
    console.error('Force logout failed:', error);
    message.error($t('system.sessions.forceLogoutFailed'));
  }
}

/**
 * 执行强制下线所有会话
 */
async function handleForceLogoutAll() {
  if (!selectedUserId.value) return;

  try {
    const formData = forceLogoutFormApi.getValues();
    await forceLogoutAllUserSessions(selectedUserId.value, {
      reason: formData.reason || undefined,
    });
    
    message.success($t('system.sessions.forceLogoutSuccess'));
    forceLogoutAllModalApi.close();
    loadUserSessions(); // 重新加载会话列表
  } catch (error) {
    console.error('Force logout all failed:', error);
    message.error($t('system.sessions.forceLogoutFailed'));
  }
}

/**
 * 刷新会话数据
 */
function onRefresh() {
  loadUserSessions();
}

// 监听用户选择变化
watch(selectedUserId, () => {
  loadUserSessions();
});

// 组件挂载时加载用户列表
loadUsers();
</script>

<template>
  <Page auto-content-height>
    <SessionDetailModal />
    <ForceLogoutModal />
    <ForceLogoutAllModal />
    
    <div class="p-4">
      <!-- 用户选择区域 -->
      <Card class="mb-4">
        <Row :gutter="16" align="middle">
          <Col :span="6">
            <div class="flex items-center space-x-2">
              <User class="size-4" />
              <span class="font-medium">{{ $t('system.sessions.userSessionManagement.selectUser') }}</span>
            </div>
          </Col>
          <Col :span="12">
            <Select
              v-model:value="selectedUserId"
              :placeholder="$t('system.sessions.userSessionManagement.selectUserPlaceholder')"
              :loading="loadingUsers"
              show-search
              :filter-option="(input: string, option: any) => 
                option.children.toLowerCase().includes(input.toLowerCase())
              "
              class="w-full"
              @change="onUserChange"
            >
              <SelectOption
                v-for="user in userList"
                :key="user.id"
                :value="user.id"
              >
                {{ user.userName }} ({{ user.fullName }})
              </SelectOption>
            </Select>
          </Col>
          <Col :span="6">
            <Space>
              <Button 
                :disabled="!hasSelectedUser || !hasSessions"
                type="primary"
                danger
                @click="onForceLogoutAll"
              >
                <LogOut class="size-4" />
                {{ $t('system.sessions.forceLogoutAll') }}
              </Button>
              <Button :disabled="!hasSelectedUser" @click="onRefresh">
                <RefreshCw class="size-4" />
                刷新
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      <!-- 会话列表区域 -->
      <Card>
        <template #title>
          <div class="flex items-center justify-between">
            <span>{{ $t('system.sessions.userSessionManagement.title') }}</span>
            <span v-if="hasSelectedUser" class="text-sm font-normal text-gray-500">
              {{ $t('system.sessions.userSessionManagement.sessionCount', { count: sessionCount }) }}
            </span>
          </div>
        </template>

        <Spin :spinning="loadingSessions">
          <div v-if="!hasSelectedUser" class="py-8">
            <Empty
              :description="$t('system.sessions.userSessionManagement.selectUserPlaceholder')"
              :image="Empty.PRESENTED_IMAGE_SIMPLE"
            />
          </div>
          
          <div v-else-if="!hasSessions && !loadingSessions" class="py-8">
            <Empty
              :description="$t('system.sessions.userSessionManagement.noSessions')"
              :image="Empty.PRESENTED_IMAGE_SIMPLE"
            />
          </div>
          
          <Grid v-else>
            <template #toolbarButtons>
              <Space>
                <Button @click="onRefresh">
                  <RefreshCw class="size-4" />
                  刷新
                </Button>
              </Space>
            </template>
          </Grid>
        </Spin>
      </Card>
    </div>
  </Page>
</template>
