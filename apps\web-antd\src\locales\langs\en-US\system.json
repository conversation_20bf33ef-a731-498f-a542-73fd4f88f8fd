{"title": "System Management", "dept": {"name": "Department", "title": "Department Management", "deptName": "Department Name", "status": "Status", "createTime": "Create Time", "remark": "Remark", "operation": "Operation", "parentDept": "Parent Department", "deptCode": "Department Code"}, "menu": {"title": "Menu Management", "parent": "<PERSON><PERSON>", "menuTitle": "Title", "menuName": "<PERSON>u Name", "name": "<PERSON><PERSON>", "type": "Type", "typeCatalog": "Catalog", "typeMenu": "<PERSON><PERSON>", "typeButton": "<PERSON><PERSON>", "typeLink": "Link", "typeEmbedded": "Embedded", "icon": "Icon", "activeIcon": "Active Icon", "activePath": "Active Path", "path": "Route Path", "component": "Component", "status": "Status", "authCode": "Auth Code", "badge": "Badge", "operation": "Operation", "linkSrc": "Link Address", "affixTab": "Affix In Tabs", "keepAlive": "Keep Alive", "hideInMenu": "Hide In Menu", "hideInTab": "Hide In Tabbar", "hideChildrenInMenu": "Hide Children In Menu", "hideInBreadcrumb": "Hide In Breadcrumb", "advancedSettings": "Other Settings", "activePathMustExist": "The path could not find a valid menu", "activePathHelp": "When jumping to the current route, \nthe menu path that needs to be activated must be specified when it does not display in the navigation menu.", "badgeType": {"title": "Badge Type", "dot": "Dot", "normal": "Text", "none": "None"}, "badgeVariants": "Badge Style"}, "role": {"title": "Role Management", "list": "Role List", "name": "Role", "roleName": "Role Name", "id": "Role ID", "status": "Status", "remark": "Remark", "createTime": "Creation Time", "operation": "Operation", "permissions": "Permissions", "setPermissions": "Permissions", "code": "Role Code"}, "user": {"title": "User Management", "name": "User", "userName": "Username", "fullName": "Full Name", "sex": "Gender", "sexMale": "Male", "sexFemale": "Female", "sexUnknown": "Unknown", "email": "Email", "emailFormatError": "Incorrect email format", "phone": "Phone", "phoneFormatError": "Incorrect phone format", "address": "Address", "icon": "Avatar", "state": "Status", "dept": "Department", "list": "User List", "searchPlaceholder": "Username/Name/Email/Phone", "userNamePlaceholder": "Please enter username", "fullNamePlaceholder": "Please enter full name", "emailPlaceholder": "Please enter email", "phonePlaceholder": "Please enter phone number", "addressPlaceholder": "Please enter address", "changeStateTitle": "Change User Status", "confirmChangeState": "Are you sure to change {name}'s status to [{status}]?", "password": "Password"}, "sessions": {"title": "Session Management", "list": "Active Sessions", "dashboard": "Session Statistics", "userSessions": "User Sessions", "sessionId": "Session ID", "userId": "User ID", "userName": "User Name", "loginTime": "Login Time", "lastActivityTime": "Last Activity Time", "tokenExpiresAt": "Token Expires At", "ipAddress": "IP Address", "userAgent": "User Agent", "deviceType": "Device Type", "operatingSystem": "Operating System", "browser": "Browser", "location": "Location", "status": "Status", "loginSource": "Login Source", "isActive": "Is Active", "forceLogout": "Force Logout", "forceLogoutAll": "Logout All Sessions", "forceLogoutConfirm": "Are you sure to force logout this session?", "forceLogoutAllConfirm": "Are you sure to force logout all sessions of user {userName}?", "forceLogoutSuccess": "Force logout successful", "forceLogoutFailed": "Force logout failed", "reason": "Logout Reason", "reasonPlaceholder": "Please enter logout reason (optional)", "statistics": {"totalOnlineUsers": "Online Users", "totalActiveSessions": "Active Sessions", "webSessions": "Web Sessions", "mobileSessions": "Mobile Sessions", "desktopSessions": "Desktop Sessions", "apiSessions": "API Sessions", "todayNewSessions": "Today New Sessions", "deviceDistribution": "Device Distribution", "browserDistribution": "Browser Distribution", "sessionTrend": "Session Trend"}, "filter": {"userNamePlaceholder": "Filter by user name", "ipAddressPlaceholder": "Filter by IP address", "deviceTypePlaceholder": "Filter by device type", "all": "All", "web": "Web", "mobile": "Mobile", "desktop": "Desktop", "api": "API"}, "detail": {"title": "Session Details", "basicInfo": "Basic Information", "deviceInfo": "Device Information", "loginInfo": "Login Information"}, "userSessionManagement": {"title": "User Session Management", "selectUser": "Select User", "selectUserPlaceholder": "Please select a user to view sessions", "noSessions": "No active sessions for this user", "sessionCount": "Session Count: {count}"}}}