<script lang="ts" setup>
import { ref } from 'vue';

import { Card, Col, Row, Statistic } from 'ant-design-vue';
import { Activity, Users } from 'lucide-vue-next';

defineOptions({
  name: 'TestStatistic',
});

const testValue = ref(1234);
</script>

<template>
  <div class="p-4">
    <h2 class="mb-4 text-xl font-bold">Statistic 组件测试</h2>
    
    <Row :gutter="[16, 16]">
      <!-- 基础用法 -->
      <Col :span="6">
        <Card>
          <Statistic
            title="基础用法"
            :value="testValue"
          />
        </Card>
      </Col>
      
      <!-- 带前缀图标 -->
      <Col :span="6">
        <Card>
          <Statistic
            title="带前缀图标"
            :value="testValue"
          >
            <template #prefix>
              <Users class="size-4" />
            </template>
          </Statistic>
        </Card>
      </Col>
      
      <!-- 带颜色样式 -->
      <Col :span="6">
        <Card>
          <Statistic
            title="带颜色样式"
            :value="testValue"
            :value-style="{ color: '#3f8600' }"
          >
            <template #prefix>
              <Activity class="size-4" />
            </template>
          </Statistic>
        </Card>
      </Col>
      
      <!-- 带后缀 -->
      <Col :span="6">
        <Card>
          <Statistic
            title="带后缀"
            :value="testValue"
            suffix="个"
            :value-style="{ color: '#1890ff' }"
          />
        </Card>
      </Col>
    </Row>
    
    <div class="mt-4">
      <p class="text-gray-600">
        如果您能看到上面的统计数字正确显示，说明 Statistic 组件工作正常。
      </p>
    </div>
  </div>
</template>
