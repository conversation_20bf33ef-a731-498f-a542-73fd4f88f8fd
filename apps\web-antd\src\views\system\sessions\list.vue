<script lang="ts" setup>
import type {
  OnActionClickParams,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';
import type { UserSessionDTO } from '#/types/platform/sessions';

import { ref } from 'vue';

import { Page, useVbenForm, useVbenModal } from '@vben/common-ui';

import { Button, message, Modal, Space } from 'ant-design-vue';
import { RefreshCw } from 'lucide-vue-next';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { forceLogoutSession, getActiveSessions } from '#/api/platform/sessions';
import { $t } from '#/locales';

import {
  useForceLogoutFormSchema,
  useSessionColumns,
  useSessionFilterSchema,
} from './data';
import SessionDetail from './modules/session-detail.vue';

defineOptions({
  name: 'SessionList',
});

// 会话详情弹窗
const [SessionDetailModal, sessionDetailModalApi] = useVbenModal({
  connectedComponent: SessionDetail,
  destroyOnClose: true,
});

// 强制下线表单弹窗
const [ForceLogoutForm, forceLogoutFormApi] = useVbenForm({
  schema: useForceLogoutFormSchema(),
});

const [ForceLogoutModal, forceLogoutModalApi] = useVbenModal({
  connectedComponent: ForceLogoutForm,
  destroyOnClose: true,
  onConfirm: async () => {
    await handleForceLogout();
  },
});

// 当前选中的会话
const currentSession = ref<UserSessionDTO | null>(null);

// 表格配置
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    schema: useSessionFilterSchema(),
    submitOnChange: true,
    resetOnWindowResize: true,
    collapsedRows: 1,
  },
  gridOptions: {
    columns: useSessionColumns(onActionClick),
    height: 'auto',
    keepSource: true,
    pagerConfig: {
      enabled: true,
      pageSize: 20,
      pageSizes: [10, 20, 50, 100],
    },
    proxyConfig: {
      ajax: {
        query: async ({ page, form }) => {
          const params = {
            pageNumber: page.currentPage,
            pageSize: page.pageSize,
            userName: form.userName || undefined,
            ipAddress: form.ipAddress || undefined,
            deviceType: form.deviceType || undefined,
          };

          const response = await getActiveSessions(params);
          
          return {
            result: response.sessions,
            page: {
              total: response.totalCount,
            },
          };
        },
      },
    },
    rowConfig: {
      keyField: 'id',
    },
    toolbarConfig: {
      slots: {
        buttons: 'toolbarButtons',
      },
      custom: true,
      export: false,
      refresh: { code: 'query' },
      zoom: true,
    },
  } as VxeTableGridOptions,
});

/**
 * 处理操作按钮点击
 */
function onActionClick({ code, row }: OnActionClickParams<UserSessionDTO>) {
  switch (code) {
    case 'forceLogout': {
      onForceLogout(row);
      break;
    }
    case 'detail': {
      onViewDetail(row);
      break;
    }
    default: {
      break;
    }
  }
}

/**
 * 查看会话详情
 */
function onViewDetail(session: UserSessionDTO) {
  sessionDetailModalApi.setData(session).open();
}

/**
 * 强制下线会话
 */
function onForceLogout(session: UserSessionDTO) {
  currentSession.value = session;
  forceLogoutModalApi
    .setData({ reason: '' })
    .setTitle($t('system.sessions.forceLogout'))
    .open();
}

/**
 * 执行强制下线
 */
async function handleForceLogout() {
  if (!currentSession.value) return;

  try {
    const formData = forceLogoutFormApi.getValues();
    await forceLogoutSession(currentSession.value.id, {
      reason: formData.reason || undefined,
    });
    
    message.success($t('system.sessions.forceLogoutSuccess'));
    forceLogoutModalApi.close();
    gridApi.reload();
  } catch (error) {
    console.error('Force logout failed:', error);
    message.error($t('system.sessions.forceLogoutFailed'));
  }
}

/**
 * 刷新数据
 */
function onRefresh() {
  gridApi.reload();
}
</script>

<template>
  <Page auto-content-height>
    <SessionDetailModal />
    <ForceLogoutModal />
    
    <Grid>
      <template #toolbarButtons>
        <Space>
          <Button @click="onRefresh">
            <RefreshCw class="size-4" />
            刷新
          </Button>
        </Space>
      </template>
    </Grid>
  </Page>
</template>
