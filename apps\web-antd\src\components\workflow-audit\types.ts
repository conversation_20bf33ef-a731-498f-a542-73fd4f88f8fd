import type { Dayjs } from 'dayjs';

/**
 * 工作流步骤信息
 */
export interface WorkflowStep {
  stepCode: string;
  stepName: string;
  description?: string;
}

/**
 * 工作流审批选项
 */
export interface WorkflowAuditOptions {
  workflowCode: string;
  stepCode: string;
  stepDetCat?: string;
  origRec: number;
}

/**
 * 工作流审批配置参数
 */
export interface WorkflowAuditConfig {
  // 是否需要电子签名
  esign: boolean;
  // 备注标题
  commentLabel: string;
  // 可选的下一个步骤列表
  nextSteps: WorkflowStep[];
  // 用户名（从store获取）
  username?: string;
}

/**
 * 工作流审批结果
 */
export interface WorkflowAuditResult {
  // 选择的下一个步骤名称
  nextStepName: string;
  // 选择的下一个步骤代码
  nextStepCode: string;
  // 备注内容
  comment: string;
  // 电子签名相关信息（如果需要）
  esigData?: {
    hasEsig: boolean;
    timestamp: Dayjs;
    username: string;
  };
}

/**
 * 工作流审批表单数据
 */
export interface WorkflowAuditFormData {
  // 用户名
  username: string;
  // 密码
  password: string;
  // 选择的下一步骤代码
  nextStepCode: string;
  // 备注
  comment: string;
}
